{"buildFiles": ["/Volumes/SSD/fvm/versions/3.29.2/packages/flutter_tools/gradle/src/main/groovy/CMakeLists.txt"], "cleanCommandsComponents": [["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Volumes/SSD/loom_dynamics/my sample/Pixs/android/app/.cxx/Debug/2k3j1vv4/arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Volumes/SSD/loom_dynamics/my sample/Pixs/android/app/.cxx/Debug/2k3j1vv4/arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}, "toolchains": {"toolchain": {"cCompilerExecutable": "/Users/<USER>/Library/Android/sdk/ndk/26.3.11579264/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang.lld", "cppCompilerExecutable": "/Users/<USER>/Library/Android/sdk/ndk/26.3.11579264/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++.lld"}}, "cFileExtensions": [], "cppFileExtensions": []}