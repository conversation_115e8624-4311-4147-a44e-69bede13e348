                        -H/Volumes/SSD/fvm/versions/3.29.2/packages/flutter_tools/gradle/src/main/groovy
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=21
-<PERSON>ANDROID_PLATFORM=android-21
-DANDROID_ABI=arm64-v8a
-DCMAKE_ANDROID_ARCH_ABI=arm64-v8a
-DANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/26.3.11579264
-DCMAKE_ANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/26.3.11579264
-DCMAKE_TOOLCHAIN_FILE=/Users/<USER>/Library/Android/sdk/ndk/26.3.11579264/build/cmake/android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=/Volumes/SSD/loom_dynamics/my sample/Pixs/build/app/intermediates/cxx/Debug/2k3j1vv4/obj/arm64-v8a
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=/Volumes/SSD/loom_dynamics/my sample/Pixs/build/app/intermediates/cxx/Debug/2k3j1vv4/obj/arm64-v8a
-DCMAKE_BUILD_TYPE=Debug
-B/Volumes/SSD/loom_dynamics/my sample/Pixs/android/app/.cxx/Debug/2k3j1vv4/arm64-v8a
-GNinja
-Wno-dev
--no-warn-unused-cli
                        Build command args: []
                        Version: 2