{"buildFiles": ["/Volumes/SSD/fvm/versions/3.29.2/packages/flutter_tools/gradle/src/main/groovy/CMakeLists.txt"], "cleanCommandsComponents": [["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Volumes/SSD/loom_dynamics/my sample/Pixs/android/app/.cxx/Debug/2k3j1vv4/x86", "clean"]], "buildTargetsCommandComponents": ["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Volumes/SSD/loom_dynamics/my sample/Pixs/android/app/.cxx/Debug/2k3j1vv4/x86", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}