import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_blurhash/flutter_blurhash.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:pixs/features/category/widgets/category_photo_shimmer.dart';
import 'package:pixs/features/home/<USER>/models/image/image_response.dart' as ImageResponse;
import 'package:pixs/features/home/<USER>/home/<USER>';
import 'package:pixs/shared/app/enums/api_fetch_status.dart';
import 'package:pixs/shared/constants/images.dart';
import 'package:pixs/shared/routes/routes.dart';
import 'package:pixs/shared/widgets/pagination/pagination_widget.dart';

import '../../home/<USER>/models/collection/collection_response.dart';

class CategoryPhotoScreen extends StatefulWidget {
  final CollectionResponse collection;
  const CategoryPhotoScreen({super.key, required this.collection});

  @override
  State<CategoryPhotoScreen> createState() => _CategoryPhotoScreenState();
}

class _CategoryPhotoScreenState extends State<CategoryPhotoScreen> {
  bool _isLoading = false;
  @override
  void initState() {
    super.initState();

    // Load photos immediately - context is ready in initState
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted && !_isLoading) {
        _loadCollectionPhotos();
      }
    });
  }

  void _loadCollectionPhotos() {
    // Prevent multiple simultaneous calls
    if (_isLoading) {
      print('Already loading, skipping duplicate call');
      return;
    }

    _isLoading = true;
    print('=== LOADING COLLECTION PHOTOS ===');
    print('Collection ID: ${widget.collection.id}');
    print('Collection Title: ${widget.collection.title}');
    print('Collection totalPhotos: ${widget.collection.totalPhotos}');

    // Validate collection data
    if (widget.collection.id == null || widget.collection.id!.isEmpty) {
      print('ERROR: Collection ID is null or empty');
      _isLoading = false;
      return;
    }

    if (widget.collection.totalPhotos == null || widget.collection.totalPhotos == 0) {
      print('WARNING: Collection reports 0 photos');
    }

    try {
      final homeCubit = context.read<HomeCubit>();
      print('HomeCubit found: ${homeCubit.hashCode}');

      // Don't clear photos here - let HomeCubit handle it
      // homeCubit.clearCollectionPhotos();

      // Make API call
      print('Calling getCollectionPhotos with ID: ${widget.collection.id}');
      homeCubit.getCollectionPhotos(
        collectionId: widget.collection.id!,
        page: 1,
      );
      print('API call initiated successfully');

      // Reset loading flag after a delay to allow for state changes
      Future.delayed(Duration(milliseconds: 500), () {
        if (mounted) {
          _isLoading = false;
        }
      });
    } catch (e) {
      print('ERROR calling getCollectionPhotos: $e');
      _isLoading = false;
    }
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      onPopInvokedWithResult: (didPop, result) {
        context.read<HomeCubit>().clearCollectionPhotos();
      },
      child: Scaffold(
        appBar: AppBar(
          title: Text(
            widget.collection.title ?? 'Collection',
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
            ),
          ),
          centerTitle: true,
        ),
        body: BlocBuilder<HomeCubit, HomeState>(
          builder: (context, state) {
            print('=== CategoryPhotoScreen BlocBuilder DEBUG ===');
            print('collectionPhotosFetchStatus: ${state.collectionPhotosFetchStatus}');
            print('collectionPhotos length: ${state.collectionPhotos?.length ?? 0}');
            print('error: ${state.error}');
            print('=== END BlocBuilder DEBUG ===');

            if (state.collectionPhotosFetchStatus == ApiFetchStatus.loading &&
                (state.collectionPhotos?.length ?? 0) == 0) {
              print('Showing loading shimmer...');
              return const CategoryPhotoShimmer();
            }

            if (state.collectionPhotosFetchStatus == ApiFetchStatus.failed &&
                (state.collectionPhotos?.length ?? 0) == 0) {
              return Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(
                      Icons.error_outline,
                      size: 64,
                      color: Colors.grey,
                    ),
                    const SizedBox(height: 16),
                    const Text(
                      'Failed to load photos',
                      style: TextStyle(fontSize: 18, color: Colors.grey),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      state.error ?? 'Unknown error occurred',
                      style: const TextStyle(fontSize: 14, color: Colors.grey),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: () {
                        context.read<HomeCubit>().getCollectionPhotos(
                          collectionId: widget.collection.id ?? '',
                        );
                      },
                      child: const Text('Retry'),
                    ),
                  ],
                ),
              );
            }

            // Handle failed state
            if (state.collectionPhotosFetchStatus == ApiFetchStatus.failed) {
              return Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.error_outline,
                      size: 64,
                      color: Colors.red,
                    ),
                    SizedBox(height: 16),
                    Text(
                      'Failed to load photos',
                      style: TextStyle(fontSize: 18, color: Colors.red),
                    ),
                    if (state.error != null) ...[
                      SizedBox(height: 8),
                      Text(
                        state.error!,
                        style: TextStyle(fontSize: 14, color: Colors.grey),
                        textAlign: TextAlign.center,
                      ),
                    ],
                    SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: () => _loadCollectionPhotos(),
                      child: Text('Retry'),
                    ),
                  ],
                ),
              );
            }

            // Handle empty state
            if ((state.collectionPhotos?.length ?? 0) == 0) {
              print('SHOWING EMPTY STATE: No photos found');
              print('Collection totalPhotos from widget: ${widget.collection.totalPhotos}');
              print('API fetch status: ${state.collectionPhotosFetchStatus}');

              return Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.photo_library_outlined,
                      size: 64,
                      color: Colors.grey,
                    ),
                    SizedBox(height: 16),
                    Text(
                      'No photos found in this collection',
                      style: TextStyle(fontSize: 18, color: Colors.grey),
                    ),
                    SizedBox(height: 8),
                    Text(
                      'This collection may be private or temporarily unavailable',
                      style: TextStyle(fontSize: 14, color: Colors.grey),
                      textAlign: TextAlign.center,
                    ),
                    SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: () => _loadCollectionPhotos(),
                      child: Text('Retry'),
                    ),
                  ],
                ),
              );
            }
            return Padding(
              padding: const EdgeInsets.all(8.0),
              child: AnimationLimiter(
                child: PaginationWidget(
                  isPaginating:
                      state.collectionPhotosFetchStatus == ApiFetchStatus.loading,
                  next: (state.collectionPhotos?.length ?? 0) <= 2000,
                  loadingText: 'Loading more photos from this collection...',
                  endText: 'All photos from this collection loaded!',
                  onPagination: (notification) {
                    if (state.collectionPhotos?.length == 2000) {
                      return false;
                    }
                    // Calculate page number correctly (assuming 30 items per page)
                    final currentItemCount = state.collectionPhotos?.length ?? 0;
                    final nextPage = (currentItemCount ~/ 30) + 1;

                    context.read<HomeCubit>().getCollectionPhotos(
                          collectionId: widget.collection.id ?? '',
                          page: nextPage,
                          isLoadMore: true,
                        );

                    return true;
                  },
                  child: GridView.builder(
                    gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: 2,
                      mainAxisSpacing: 8.0,
                      crossAxisSpacing: 8.0,
                    ),
                    itemCount: state.collectionPhotos?.length ?? 0,
                    itemBuilder: (BuildContext context, int index) =>
                        AnimationConfiguration.staggeredGrid(
                      position: index,
                      duration: const Duration(milliseconds: 375),
                      columnCount: 2,
                      child: ScaleAnimation(
                        child: FadeInAnimation(
                          child: GestureDetector(
                            onTap: () => Navigator.pushNamed(
                              context,
                              routeImageDetail,
                              arguments: {
                                'image': ImageResponse.ImageResponse(
                                  urls: ImageResponse.Urls(
                                    small: state.collectionPhotos?[index].urls?.small,
                                    full: state.collectionPhotos?[index].urls?.full,
                                    raw: state.collectionPhotos?[index].urls?.raw,
                                    regular: state.collectionPhotos?[index].urls?.regular,
                                    thumb: state.collectionPhotos?[index].urls?.thumb,
                                  ),
                                  blurHash: state.collectionPhotos?[index].blurHash,
                                  id: state.collectionPhotos?[index].id,
                                  width: state.collectionPhotos?[index].width,
                                  height: state.collectionPhotos?[index].height,
                                  color: state.collectionPhotos?[index].color,
                                  likes: state.collectionPhotos?[index].likes,
                                  likedByUser: state.collectionPhotos?[index].likedByUser,
                                  description: state.collectionPhotos?[index].description,
                                  user: ImageResponse.User(
                                    username: state.collectionPhotos?[index].user?.username,
                                    profileImage: ImageResponse.ProfileImage(
                                      small: state.collectionPhotos?[index].user?.profileImage?.small,
                                      medium: state.collectionPhotos?[index].user?.profileImage?.medium,
                                      large: state.collectionPhotos?[index].user?.profileImage?.large,
                                    ),
                                    bio: state.collectionPhotos?[index].user?.bio,
                                    name: state.collectionPhotos?[index].user?.name,
                                    totalCollections: state.collectionPhotos?[index].user?.totalCollections,
                                    totalLikes: state.collectionPhotos?[index].user?.totalLikes,
                                    totalPhotos: state.collectionPhotos?[index].user?.totalPhotos,
                                    instagramUsername: state.collectionPhotos?[index].user?.instagramUsername,
                                  ),
                                  currentUserCollections: state.collectionPhotos?[index].currentUserCollections?.map((e) => ImageResponse.CurrentUserCollection.fromJson(e.toJson())).toList(),
                                )
                              },
                            ),
                            child: Container(
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(15.r),
                                color: Colors.black,
      
                                // color: Color((Random().nextDouble() * 0xFFFFFF)
                                //         .toInt())
                                //     .withOpacity(0.2),
                              ),
                              child: ClipRRect(
                                borderRadius: BorderRadius.circular(15.r),
                                child: CachedNetworkImage(
                                  cacheKey:
                                      state.collectionPhotos?[index].urls?.small,
                                  fit: BoxFit.cover,
                                  imageUrl: state
                                          .collectionPhotos?[index].urls?.small ??
                                      '',
                                  progressIndicatorBuilder:
                                      (context, url, progress) => Opacity(
                                    opacity: 0.5,
                                    child: Center(
                                      child: (state.collectionPhotos?[index]
                                                      .blurHash?.length ??
                                                  0) >=
                                              6
                                          ? BlurHash(
                                              hash: state.collectionPhotos?[index]
                                                      .blurHash ??
                                                  'LGF5]+Yk^6#M@-5c,1J5@[or[Q6.',
                                            )
                                          : Container(
                                              color: Colors.grey[300],
                                              child: const Center(
                                                child: CircularProgressIndicator(),
                                              ),
                                            ),
                                    ),
                                  ),
                                  errorWidget: (context, url, error) => Center(
                                    child: Opacity(
                                      opacity: 0.5,
                                      child: SizedBox(
                                        width: 50,
                                        child: Center(
                                          child: Image.asset(
                                            Assets.kLogo,
                                            width: 50,
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}
