import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:pixs/shared/widgets/shimmer/shimmer_widget.dart';

class CategoryPhotoShimmer extends StatelessWidget {
  const CategoryPhotoShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(top: 24.h, left: 16.w, right: 16.w),
      child: ListView.separated(
        itemCount: 4,
        separatorBuilder: (context, index) {
          return 24.verticalSpace;
        },
        itemBuilder: (context, index) {
          return Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              ShimmerWidget(
                height: 160.h,
                width: 160.w,
                radius: 20,
                isLoading: true,
              ),
              16.horizontalSpace,
              ShimmerWidget(
                height: 160.h,
                width: 160.w,
                radius: 20,
                isLoading: true,
              ),
            ],
          );
        },
      ),
    );
  }
}
