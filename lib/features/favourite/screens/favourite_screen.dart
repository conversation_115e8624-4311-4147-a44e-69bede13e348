import 'dart:math';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_blurhash/flutter_blurhash.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:pixs/features/home/<USER>/home/<USER>';
import 'package:pixs/features/image_details/widgets/animated_favorite_button.dart';
import 'package:pixs/shared/constants/images.dart';
import 'package:pixs/shared/routes/routes.dart';
import 'package:pixs/shared/widgets/attribution_widget.dart';

class FavouriteScreen extends StatelessWidget {
  const FavouriteScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: BlocBuilder<HomeCubit, HomeState>(
        builder: (context, state) {
          if (state.wishListImages?.isEmpty ?? true) {
            return const Center(
              child: Text('No images'),
            );
          }
          return GridView.builder(
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              mainAxisSpacing: 8.0,
              crossAxisSpacing: 8.0,
            ),
            itemCount: state.wishListImages?.length ?? 0,
            itemBuilder: (BuildContext context, int index) =>
                AnimationConfiguration.staggeredGrid(
              position: index,
              duration: const Duration(milliseconds: 375),
              columnCount: 2,
              child: ScaleAnimation(
                child: FadeInAnimation(
                  child: GestureDetector(
                    onTap: () => Navigator.pushNamed(
                      context,
                      routeImageDetail,
                      arguments: {'image': state.wishListImages?[index]},
                    ),
                    child: Stack(
                      children: [
                        // Image container
                        Container(
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(15.r),
                            color: Color((Random().nextDouble() * 0xFFFFFF).toInt())
                                .withOpacity(0.2),
                          ),
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(15.r),
                            child: Stack(
                              children: [
                                CachedNetworkImage(
                                  cacheKey: state.wishListImages?[index].urls?.full,
                                  maxWidthDiskCache: 1024,
                                  maxHeightDiskCache: 1024,
                                  fit: BoxFit.cover,
                                  width: double.infinity,
                                  height: double.infinity,
                                  imageUrl:
                                      state.wishListImages?[index].urls?.full ?? '',
                                  progressIndicatorBuilder: (context, url, progress) =>
                                      Opacity(
                                    opacity: 0.5,
                                    child: Center(
                                      child: BlurHash(
                                        hash:
                                            state.wishListImages?[index].blurHash ?? '',
                                      ),
                                    ),
                                  ),
                                  errorWidget: (context, url, error) => Center(
                                    child: Opacity(
                                      opacity: 0.5,
                                      child: SizedBox(
                                        width: 50,
                                        child: Center(
                                          child: Image.asset(
                                            Assets.kLogo,
                                            width: 50,
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                                // Compact attribution at bottom
                                if (state.wishListImages?[index] != null)
                                  Positioned(
                                    bottom: 8.h,
                                    left: 8.w,
                                    right: 8.w,
                                    child: CompactAttributionWidget(
                                      image: state.wishListImages![index],
                                    ),
                                  ),
                              ],
                            ),
                          ),
                        ),

                        // Favorite button overlay
                        Positioned(
                          top: 0,
                          right: 0,
                          child: AnimatedFavoriteButton(
                            image: state.wishListImages![index],
                            isWishList: true, // Always true in favorites screen
                            onToggle: (isFavorite) {
                              if (!isFavorite) {
                                context.read<HomeCubit>().removeFromWishList(state.wishListImages![index]);
                              }
                            },
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}
