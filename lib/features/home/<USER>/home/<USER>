part of 'home_cubit.dart';

final class HomeState extends Equatable {
  final List<ImageResponse>? images;
  final ApiFetchStatus imageListFetchStatus;
  final List<ImageResponse>? userImages;
  final List<ImageResponse>? wishListImages;
  final ApiFetchStatus userImageListFetchStatus;
  final List<CollectionResponse>? collections;
  final ApiFetchStatus collectionListFetchStatus;
  final int currentCollectionPage;
  final bool hasMoreCollections;
  final bool isLoadingMoreCollections;
  final List<CollectionPhotoListResponse>? collectionPhotos;
  final ApiFetchStatus collectionPhotosFetchStatus;
  final bool isWallpaperSetting;
  final int? clickedCardIndex;
  final String? prompt;
  final AiResponse? imageGenerationResult;
  final ApiFetchStatus imageGenerationStatus;
  final String? error;
  const HomeState(
      {this.images,
      this.imageListFetchStatus = ApiFetchStatus.idle,
      this.userImages,
      this.userImageListFetchStatus = ApiFetchStatus.idle,
      this.isWallpaperSetting = false,
      this.wishListImages,
      this.collections,
      this.collectionListFetchStatus = ApiFetchStatus.idle,
      this.currentCollectionPage = 1,
      this.hasMoreCollections = true,
      this.isLoadingMoreCollections = false,
      this.collectionPhotos,
      this.collectionPhotosFetchStatus = ApiFetchStatus.idle,
      this.clickedCardIndex,
      this.error,
      this.prompt,
      this.imageGenerationResult,
      this.imageGenerationStatus = ApiFetchStatus.idle});

  @override
  List<Object?> get props => [
        images,
        imageListFetchStatus,
        userImages,
        userImageListFetchStatus,
        isWallpaperSetting,
        error,
        wishListImages,
        collections,
        collectionListFetchStatus,
        currentCollectionPage,
        hasMoreCollections,
        isLoadingMoreCollections,
        collectionPhotos,
        collectionPhotosFetchStatus,
        clickedCardIndex,
        prompt,
        imageGenerationResult,
        imageGenerationStatus,
      ];

  HomeState copyWith(
      {List<ImageResponse>? images,
      ApiFetchStatus? imageListFetchStatus,
      List<ImageResponse>? userImages,
      ApiFetchStatus? userImageListFetchStatus,
      bool? isWallpaperSetting,
      String? error,
      List<ImageResponse>? wishListImages,
      List<CollectionResponse>? collections,
      ApiFetchStatus? collectionListFetchStatus,
      int? currentCollectionPage,
      bool? hasMoreCollections,
      bool? isLoadingMoreCollections,
      List<CollectionPhotoListResponse>? collectionPhotos,
      ApiFetchStatus? collectionPhotosFetchStatus,
      int? clickedCardIndex,
      String? prompt,
      AiResponse? imageGenerationResult,
      ApiFetchStatus? imageGenerationStatus}) {
    return HomeState(
        images: images ?? this.images,
        imageListFetchStatus: imageListFetchStatus ?? this.imageListFetchStatus,
        userImages: userImages ?? this.userImages,
        userImageListFetchStatus: userImageListFetchStatus ?? this.userImageListFetchStatus,
        isWallpaperSetting: isWallpaperSetting ?? this.isWallpaperSetting,
        error: error ?? this.error,
        wishListImages: wishListImages ?? this.wishListImages,
        collections: collections ?? this.collections,
        collectionListFetchStatus: collectionListFetchStatus ?? this.collectionListFetchStatus,
        currentCollectionPage: currentCollectionPage ?? this.currentCollectionPage,
        hasMoreCollections: hasMoreCollections ?? this.hasMoreCollections,
        isLoadingMoreCollections: isLoadingMoreCollections ?? this.isLoadingMoreCollections,
        collectionPhotos: collectionPhotos ?? this.collectionPhotos,
        collectionPhotosFetchStatus: collectionPhotosFetchStatus ?? this.collectionPhotosFetchStatus,
        clickedCardIndex: clickedCardIndex ?? this.clickedCardIndex,
        prompt: prompt ?? this.prompt,
        imageGenerationResult: imageGenerationResult ?? this.imageGenerationResult,
        imageGenerationStatus: imageGenerationStatus ?? this.imageGenerationStatus);
  }
}
