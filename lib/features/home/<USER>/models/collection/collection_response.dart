// To parse this JSON data, do
//
//     final collectionResponse = collectionResponseFromJson(jsonString);

import 'package:freezed_annotation/freezed_annotation.dart';
import 'dart:convert';

part 'collection_response.freezed.dart';
part 'collection_response.g.dart';

List<CollectionResponse> collectionResponseFromJson( str) => List<CollectionResponse>.from((str).map((x) => CollectionResponse.fromJson(x)));

String collectionResponseToJson(List<CollectionResponse> data) => json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

@freezed
class CollectionResponse with _$CollectionResponse {
    const factory CollectionResponse({
        @JsonKey(name: "id")
        String? id,
        @JsonKey(name: "title")
        String? title,
        @JsonKey(name: "description")
        String? description,
        @JsonKey(name: "published_at")
        DateTime? publishedAt,
        @Json<PERSON>ey(name: "last_collected_at")
        DateTime? lastCollectedAt,
        @Json<PERSON>ey(name: "total_photos")
        int? totalPhotos,
        @Json<PERSON>ey(name: "private")
        bool? private,
        @Json<PERSON>ey(name: "share_key")
        String? shareKey,
        @JsonKey(name: "cover_photo")
        CoverPhoto? coverPhoto,
        @JsonKey(name: "user")
        User? user,
        @JsonKey(name: "links")
        CollectionResponseLinks? links,
    }) = _CollectionResponse;

    factory CollectionResponse.fromJson(Map<String, dynamic> json) => _$CollectionResponseFromJson(json);
}

@freezed
class CoverPhoto with _$CoverPhoto {
    const factory CoverPhoto({
        @JsonKey(name: "id")
        String? id,
        @JsonKey(name: "width")
        int? width,
        @JsonKey(name: "height")
        int? height,
        @JsonKey(name: "color")
        String? color,
        @JsonKey(name: "blur_hash")
        String? blurHash,
        @JsonKey(name: "likes")
        int? likes,
        @JsonKey(name: "liked_by_user")
        bool? likedByUser,
        @JsonKey(name: "description")
        String? description,
        @JsonKey(name: "user")
        User? user,
        @JsonKey(name: "urls")
        Urls? urls,
        @JsonKey(name: "links")
        CoverPhotoLinks? links,
    }) = _CoverPhoto;

    factory CoverPhoto.fromJson(Map<String, dynamic> json) => _$CoverPhotoFromJson(json);
}

@freezed
class CoverPhotoLinks with _$CoverPhotoLinks {
    const factory CoverPhotoLinks({
        @JsonKey(name: "self")
        String? self,
        @JsonKey(name: "html")
        String? html,
        @JsonKey(name: "download")
        String? download,
    }) = _CoverPhotoLinks;

    factory CoverPhotoLinks.fromJson(Map<String, dynamic> json) => _$CoverPhotoLinksFromJson(json);
}

@freezed
class Urls with _$Urls {
    const factory Urls({
        @JsonKey(name: "raw")
        String? raw,
        @JsonKey(name: "full")
        String? full,
        @JsonKey(name: "regular")
        String? regular,
        @JsonKey(name: "small")
        String? small,
        @JsonKey(name: "thumb")
        String? thumb,
    }) = _Urls;

    factory Urls.fromJson(Map<String, dynamic> json) => _$UrlsFromJson(json);
}

@freezed
class User with _$User {
    const factory User({
        @JsonKey(name: "id")
        String? id,
        @JsonKey(name: "username")
        String? username,
        @JsonKey(name: "name")
        String? name,
        @JsonKey(name: "portfolio_url")
        String? portfolioUrl,
        @JsonKey(name: "bio")
        String? bio,
        @JsonKey(name: "location")
        String? location,
        @JsonKey(name: "total_likes")
        int? totalLikes,
        @JsonKey(name: "total_photos")
        int? totalPhotos,
        @JsonKey(name: "total_collections")
        int? totalCollections,
        @JsonKey(name: "profile_image")
        ProfileImage? profileImage,
        @JsonKey(name: "links")
        UserLinks? links,
        @JsonKey(name: "updated_at")
        DateTime? updatedAt,
    }) = _User;

    factory User.fromJson(Map<String, dynamic> json) => _$UserFromJson(json);
}

@freezed
class UserLinks with _$UserLinks {
    const factory UserLinks({
        @JsonKey(name: "self")
        String? self,
        @JsonKey(name: "html")
        String? html,
        @JsonKey(name: "photos")
        String? photos,
        @JsonKey(name: "likes")
        String? likes,
        @JsonKey(name: "portfolio")
        String? portfolio,
    }) = _UserLinks;

    factory UserLinks.fromJson(Map<String, dynamic> json) => _$UserLinksFromJson(json);
}

@freezed
class ProfileImage with _$ProfileImage {
    const factory ProfileImage({
        @JsonKey(name: "small")
        String? small,
        @JsonKey(name: "medium")
        String? medium,
        @JsonKey(name: "large")
        String? large,
    }) = _ProfileImage;

    factory ProfileImage.fromJson(Map<String, dynamic> json) => _$ProfileImageFromJson(json);
}

@freezed
class CollectionResponseLinks with _$CollectionResponseLinks {
    const factory CollectionResponseLinks({
        @JsonKey(name: "self")
        String? self,
        @JsonKey(name: "html")
        String? html,
        @JsonKey(name: "photos")
        String? photos,
        @JsonKey(name: "related")
        String? related,
    }) = _CollectionResponseLinks;

    factory CollectionResponseLinks.fromJson(Map<String, dynamic> json) => _$CollectionResponseLinksFromJson(json);
}
