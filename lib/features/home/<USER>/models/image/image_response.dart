// To parse this JSON data, do
//
//     final imageResponse = imageResponseFromJson(jsonString);

import 'package:freezed_annotation/freezed_annotation.dart';
import 'dart:convert';

part 'image_response.freezed.dart';
part 'image_response.g.dart';

List<ImageResponse> imageResponseFromJson( str) => List<ImageResponse>.from((str).map((x) => ImageResponse.fromJson(x)));

String imageResponseToJson(List<ImageResponse> data) => json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

@freezed
class ImageResponse with _$ImageResponse {
    const factory ImageResponse({
        @Json<PERSON>ey(name: "id")
        String? id,
        @Json<PERSON>ey(name: "created_at")
        DateTime? createdAt,
        @Json<PERSON>ey(name: "updated_at")
        DateTime? updatedAt,
        @Json<PERSON><PERSON>(name: "width")
        int? width,
        @J<PERSON><PERSON><PERSON>(name: "height")
        int? height,
        @J<PERSON><PERSON><PERSON>(name: "color")
        String? color,
        @Json<PERSON><PERSON>(name: "blur_hash")
        String? blurHash,
        @Json<PERSON><PERSON>(name: "likes")
        int? likes,
        @<PERSON>son<PERSON>ey(name: "liked_by_user")
        bool? likedByUser,
        @JsonKey(name: "description")
        String? description,
        @JsonKey(name: "user")
        User? user,
        @JsonKey(name: "current_user_collections")
        List<CurrentUserCollection>? currentUserCollections,
        @JsonKey(name: "urls")
        Urls? urls,
        @JsonKey(name: "links")
        ImageResponseLinks? links,
    }) = _ImageResponse;

    factory ImageResponse.fromJson(Map<String, dynamic> json) => _$ImageResponseFromJson(json);
}

@freezed
class CurrentUserCollection with _$CurrentUserCollection {
    const factory CurrentUserCollection({
        @JsonKey(name: "id")
        int? id,
        @JsonKey(name: "title")
        String? title,
        @JsonKey(name: "published_at")
        DateTime? publishedAt,
        @JsonKey(name: "last_collected_at")
        DateTime? lastCollectedAt,
        @JsonKey(name: "updated_at")
        DateTime? updatedAt,
        @JsonKey(name: "cover_photo")
        dynamic coverPhoto,
        @JsonKey(name: "user")
        dynamic user,
    }) = _CurrentUserCollection;

    factory CurrentUserCollection.fromJson(Map<String, dynamic> json) => _$CurrentUserCollectionFromJson(json);
}

@freezed
class ImageResponseLinks with _$ImageResponseLinks {
    const factory ImageResponseLinks({
        @JsonKey(name: "self")
        String? self,
        @JsonKey(name: "html")
        String? html,
        @JsonKey(name: "download")
        String? download,
        @JsonKey(name: "download_location")
        String? downloadLocation,
    }) = _ImageResponseLinks;

    factory ImageResponseLinks.fromJson(Map<String, dynamic> json) => _$ImageResponseLinksFromJson(json);
}

@freezed
class Urls with _$Urls {
    const factory Urls({
        @JsonKey(name: "raw")
        String? raw,
        @JsonKey(name: "full")
        String? full,
        @JsonKey(name: "regular")
        String? regular,
        @JsonKey(name: "small")
        String? small,
        @JsonKey(name: "thumb")
        String? thumb,
    }) = _Urls;

    factory Urls.fromJson(Map<String, dynamic> json) => _$UrlsFromJson(json);
}

@freezed
class User with _$User {
    const factory User({
        @JsonKey(name: "id")
        String? id,
        @JsonKey(name: "username")
        String? username,
        @JsonKey(name: "name")
        String? name,
        @JsonKey(name: "portfolio_url")
        String? portfolioUrl,
        @JsonKey(name: "bio")
        String? bio,
        @JsonKey(name: "location")
        String? location,
        @JsonKey(name: "total_likes")
        int? totalLikes,
        @JsonKey(name: "total_photos")
        int? totalPhotos,
        @JsonKey(name: "total_collections")
        int? totalCollections,
        @JsonKey(name: "instagram_username")
        String? instagramUsername,
        @JsonKey(name: "twitter_username")
        String? twitterUsername,
        @JsonKey(name: "profile_image")
        ProfileImage? profileImage,
        @JsonKey(name: "links")
        UserLinks? links,
    }) = _User;

    factory User.fromJson(Map<String, dynamic> json) => _$UserFromJson(json);
}

@freezed
class UserLinks with _$UserLinks {
    const factory UserLinks({
        @JsonKey(name: "self")
        String? self,
        @JsonKey(name: "html")
        String? html,
        @JsonKey(name: "photos")
        String? photos,
        @JsonKey(name: "likes")
        String? likes,
        @JsonKey(name: "portfolio")
        String? portfolio,
    }) = _UserLinks;

    factory UserLinks.fromJson(Map<String, dynamic> json) => _$UserLinksFromJson(json);
}

@freezed
class ProfileImage with _$ProfileImage {
    const factory ProfileImage({
        @JsonKey(name: "small")
        String? small,
        @JsonKey(name: "medium")
        String? medium,
        @JsonKey(name: "large")
        String? large,
    }) = _ProfileImage;

    factory ProfileImage.fromJson(Map<String, dynamic> json) => _$ProfileImageFromJson(json);
}
