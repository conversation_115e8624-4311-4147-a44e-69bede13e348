// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'image_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

ImageResponse _$ImageResponseFromJson(Map<String, dynamic> json) {
  return _ImageResponse.fromJson(json);
}

/// @nodoc
mixin _$ImageResponse {
  @JsonKey(name: "id")
  String? get id => throw _privateConstructorUsedError;
  @JsonKey(name: "created_at")
  DateTime? get createdAt => throw _privateConstructorUsedError;
  @JsonKey(name: "updated_at")
  DateTime? get updatedAt => throw _privateConstructorUsedError;
  @JsonKey(name: "width")
  int? get width => throw _privateConstructorUsedError;
  @JsonKey(name: "height")
  int? get height => throw _privateConstructorUsedError;
  @JsonKey(name: "color")
  String? get color => throw _privateConstructorUsedError;
  @JsonKey(name: "blur_hash")
  String? get blurHash => throw _privateConstructorUsedError;
  @JsonKey(name: "likes")
  int? get likes => throw _privateConstructorUsedError;
  @JsonKey(name: "liked_by_user")
  bool? get likedByUser => throw _privateConstructorUsedError;
  @JsonKey(name: "description")
  String? get description => throw _privateConstructorUsedError;
  @JsonKey(name: "user")
  User? get user => throw _privateConstructorUsedError;
  @JsonKey(name: "current_user_collections")
  List<CurrentUserCollection>? get currentUserCollections =>
      throw _privateConstructorUsedError;
  @JsonKey(name: "urls")
  Urls? get urls => throw _privateConstructorUsedError;
  @JsonKey(name: "links")
  ImageResponseLinks? get links => throw _privateConstructorUsedError;

  /// Serializes this ImageResponse to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ImageResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ImageResponseCopyWith<ImageResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ImageResponseCopyWith<$Res> {
  factory $ImageResponseCopyWith(
          ImageResponse value, $Res Function(ImageResponse) then) =
      _$ImageResponseCopyWithImpl<$Res, ImageResponse>;
  @useResult
  $Res call(
      {@JsonKey(name: "id") String? id,
      @JsonKey(name: "created_at") DateTime? createdAt,
      @JsonKey(name: "updated_at") DateTime? updatedAt,
      @JsonKey(name: "width") int? width,
      @JsonKey(name: "height") int? height,
      @JsonKey(name: "color") String? color,
      @JsonKey(name: "blur_hash") String? blurHash,
      @JsonKey(name: "likes") int? likes,
      @JsonKey(name: "liked_by_user") bool? likedByUser,
      @JsonKey(name: "description") String? description,
      @JsonKey(name: "user") User? user,
      @JsonKey(name: "current_user_collections")
      List<CurrentUserCollection>? currentUserCollections,
      @JsonKey(name: "urls") Urls? urls,
      @JsonKey(name: "links") ImageResponseLinks? links});

  $UserCopyWith<$Res>? get user;
  $UrlsCopyWith<$Res>? get urls;
  $ImageResponseLinksCopyWith<$Res>? get links;
}

/// @nodoc
class _$ImageResponseCopyWithImpl<$Res, $Val extends ImageResponse>
    implements $ImageResponseCopyWith<$Res> {
  _$ImageResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ImageResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
    Object? width = freezed,
    Object? height = freezed,
    Object? color = freezed,
    Object? blurHash = freezed,
    Object? likes = freezed,
    Object? likedByUser = freezed,
    Object? description = freezed,
    Object? user = freezed,
    Object? currentUserCollections = freezed,
    Object? urls = freezed,
    Object? links = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      width: freezed == width
          ? _value.width
          : width // ignore: cast_nullable_to_non_nullable
              as int?,
      height: freezed == height
          ? _value.height
          : height // ignore: cast_nullable_to_non_nullable
              as int?,
      color: freezed == color
          ? _value.color
          : color // ignore: cast_nullable_to_non_nullable
              as String?,
      blurHash: freezed == blurHash
          ? _value.blurHash
          : blurHash // ignore: cast_nullable_to_non_nullable
              as String?,
      likes: freezed == likes
          ? _value.likes
          : likes // ignore: cast_nullable_to_non_nullable
              as int?,
      likedByUser: freezed == likedByUser
          ? _value.likedByUser
          : likedByUser // ignore: cast_nullable_to_non_nullable
              as bool?,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      user: freezed == user
          ? _value.user
          : user // ignore: cast_nullable_to_non_nullable
              as User?,
      currentUserCollections: freezed == currentUserCollections
          ? _value.currentUserCollections
          : currentUserCollections // ignore: cast_nullable_to_non_nullable
              as List<CurrentUserCollection>?,
      urls: freezed == urls
          ? _value.urls
          : urls // ignore: cast_nullable_to_non_nullable
              as Urls?,
      links: freezed == links
          ? _value.links
          : links // ignore: cast_nullable_to_non_nullable
              as ImageResponseLinks?,
    ) as $Val);
  }

  /// Create a copy of ImageResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $UserCopyWith<$Res>? get user {
    if (_value.user == null) {
      return null;
    }

    return $UserCopyWith<$Res>(_value.user!, (value) {
      return _then(_value.copyWith(user: value) as $Val);
    });
  }

  /// Create a copy of ImageResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $UrlsCopyWith<$Res>? get urls {
    if (_value.urls == null) {
      return null;
    }

    return $UrlsCopyWith<$Res>(_value.urls!, (value) {
      return _then(_value.copyWith(urls: value) as $Val);
    });
  }

  /// Create a copy of ImageResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ImageResponseLinksCopyWith<$Res>? get links {
    if (_value.links == null) {
      return null;
    }

    return $ImageResponseLinksCopyWith<$Res>(_value.links!, (value) {
      return _then(_value.copyWith(links: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$ImageResponseImplCopyWith<$Res>
    implements $ImageResponseCopyWith<$Res> {
  factory _$$ImageResponseImplCopyWith(
          _$ImageResponseImpl value, $Res Function(_$ImageResponseImpl) then) =
      __$$ImageResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: "id") String? id,
      @JsonKey(name: "created_at") DateTime? createdAt,
      @JsonKey(name: "updated_at") DateTime? updatedAt,
      @JsonKey(name: "width") int? width,
      @JsonKey(name: "height") int? height,
      @JsonKey(name: "color") String? color,
      @JsonKey(name: "blur_hash") String? blurHash,
      @JsonKey(name: "likes") int? likes,
      @JsonKey(name: "liked_by_user") bool? likedByUser,
      @JsonKey(name: "description") String? description,
      @JsonKey(name: "user") User? user,
      @JsonKey(name: "current_user_collections")
      List<CurrentUserCollection>? currentUserCollections,
      @JsonKey(name: "urls") Urls? urls,
      @JsonKey(name: "links") ImageResponseLinks? links});

  @override
  $UserCopyWith<$Res>? get user;
  @override
  $UrlsCopyWith<$Res>? get urls;
  @override
  $ImageResponseLinksCopyWith<$Res>? get links;
}

/// @nodoc
class __$$ImageResponseImplCopyWithImpl<$Res>
    extends _$ImageResponseCopyWithImpl<$Res, _$ImageResponseImpl>
    implements _$$ImageResponseImplCopyWith<$Res> {
  __$$ImageResponseImplCopyWithImpl(
      _$ImageResponseImpl _value, $Res Function(_$ImageResponseImpl) _then)
      : super(_value, _then);

  /// Create a copy of ImageResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
    Object? width = freezed,
    Object? height = freezed,
    Object? color = freezed,
    Object? blurHash = freezed,
    Object? likes = freezed,
    Object? likedByUser = freezed,
    Object? description = freezed,
    Object? user = freezed,
    Object? currentUserCollections = freezed,
    Object? urls = freezed,
    Object? links = freezed,
  }) {
    return _then(_$ImageResponseImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      width: freezed == width
          ? _value.width
          : width // ignore: cast_nullable_to_non_nullable
              as int?,
      height: freezed == height
          ? _value.height
          : height // ignore: cast_nullable_to_non_nullable
              as int?,
      color: freezed == color
          ? _value.color
          : color // ignore: cast_nullable_to_non_nullable
              as String?,
      blurHash: freezed == blurHash
          ? _value.blurHash
          : blurHash // ignore: cast_nullable_to_non_nullable
              as String?,
      likes: freezed == likes
          ? _value.likes
          : likes // ignore: cast_nullable_to_non_nullable
              as int?,
      likedByUser: freezed == likedByUser
          ? _value.likedByUser
          : likedByUser // ignore: cast_nullable_to_non_nullable
              as bool?,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      user: freezed == user
          ? _value.user
          : user // ignore: cast_nullable_to_non_nullable
              as User?,
      currentUserCollections: freezed == currentUserCollections
          ? _value._currentUserCollections
          : currentUserCollections // ignore: cast_nullable_to_non_nullable
              as List<CurrentUserCollection>?,
      urls: freezed == urls
          ? _value.urls
          : urls // ignore: cast_nullable_to_non_nullable
              as Urls?,
      links: freezed == links
          ? _value.links
          : links // ignore: cast_nullable_to_non_nullable
              as ImageResponseLinks?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ImageResponseImpl implements _ImageResponse {
  const _$ImageResponseImpl(
      {@JsonKey(name: "id") this.id,
      @JsonKey(name: "created_at") this.createdAt,
      @JsonKey(name: "updated_at") this.updatedAt,
      @JsonKey(name: "width") this.width,
      @JsonKey(name: "height") this.height,
      @JsonKey(name: "color") this.color,
      @JsonKey(name: "blur_hash") this.blurHash,
      @JsonKey(name: "likes") this.likes,
      @JsonKey(name: "liked_by_user") this.likedByUser,
      @JsonKey(name: "description") this.description,
      @JsonKey(name: "user") this.user,
      @JsonKey(name: "current_user_collections")
      final List<CurrentUserCollection>? currentUserCollections,
      @JsonKey(name: "urls") this.urls,
      @JsonKey(name: "links") this.links})
      : _currentUserCollections = currentUserCollections;

  factory _$ImageResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$ImageResponseImplFromJson(json);

  @override
  @JsonKey(name: "id")
  final String? id;
  @override
  @JsonKey(name: "created_at")
  final DateTime? createdAt;
  @override
  @JsonKey(name: "updated_at")
  final DateTime? updatedAt;
  @override
  @JsonKey(name: "width")
  final int? width;
  @override
  @JsonKey(name: "height")
  final int? height;
  @override
  @JsonKey(name: "color")
  final String? color;
  @override
  @JsonKey(name: "blur_hash")
  final String? blurHash;
  @override
  @JsonKey(name: "likes")
  final int? likes;
  @override
  @JsonKey(name: "liked_by_user")
  final bool? likedByUser;
  @override
  @JsonKey(name: "description")
  final String? description;
  @override
  @JsonKey(name: "user")
  final User? user;
  final List<CurrentUserCollection>? _currentUserCollections;
  @override
  @JsonKey(name: "current_user_collections")
  List<CurrentUserCollection>? get currentUserCollections {
    final value = _currentUserCollections;
    if (value == null) return null;
    if (_currentUserCollections is EqualUnmodifiableListView)
      return _currentUserCollections;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  @JsonKey(name: "urls")
  final Urls? urls;
  @override
  @JsonKey(name: "links")
  final ImageResponseLinks? links;

  @override
  String toString() {
    return 'ImageResponse(id: $id, createdAt: $createdAt, updatedAt: $updatedAt, width: $width, height: $height, color: $color, blurHash: $blurHash, likes: $likes, likedByUser: $likedByUser, description: $description, user: $user, currentUserCollections: $currentUserCollections, urls: $urls, links: $links)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ImageResponseImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt) &&
            (identical(other.width, width) || other.width == width) &&
            (identical(other.height, height) || other.height == height) &&
            (identical(other.color, color) || other.color == color) &&
            (identical(other.blurHash, blurHash) ||
                other.blurHash == blurHash) &&
            (identical(other.likes, likes) || other.likes == likes) &&
            (identical(other.likedByUser, likedByUser) ||
                other.likedByUser == likedByUser) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.user, user) || other.user == user) &&
            const DeepCollectionEquality().equals(
                other._currentUserCollections, _currentUserCollections) &&
            (identical(other.urls, urls) || other.urls == urls) &&
            (identical(other.links, links) || other.links == links));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      createdAt,
      updatedAt,
      width,
      height,
      color,
      blurHash,
      likes,
      likedByUser,
      description,
      user,
      const DeepCollectionEquality().hash(_currentUserCollections),
      urls,
      links);

  /// Create a copy of ImageResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ImageResponseImplCopyWith<_$ImageResponseImpl> get copyWith =>
      __$$ImageResponseImplCopyWithImpl<_$ImageResponseImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ImageResponseImplToJson(
      this,
    );
  }
}

abstract class _ImageResponse implements ImageResponse {
  const factory _ImageResponse(
          {@JsonKey(name: "id") final String? id,
          @JsonKey(name: "created_at") final DateTime? createdAt,
          @JsonKey(name: "updated_at") final DateTime? updatedAt,
          @JsonKey(name: "width") final int? width,
          @JsonKey(name: "height") final int? height,
          @JsonKey(name: "color") final String? color,
          @JsonKey(name: "blur_hash") final String? blurHash,
          @JsonKey(name: "likes") final int? likes,
          @JsonKey(name: "liked_by_user") final bool? likedByUser,
          @JsonKey(name: "description") final String? description,
          @JsonKey(name: "user") final User? user,
          @JsonKey(name: "current_user_collections")
          final List<CurrentUserCollection>? currentUserCollections,
          @JsonKey(name: "urls") final Urls? urls,
          @JsonKey(name: "links") final ImageResponseLinks? links}) =
      _$ImageResponseImpl;

  factory _ImageResponse.fromJson(Map<String, dynamic> json) =
      _$ImageResponseImpl.fromJson;

  @override
  @JsonKey(name: "id")
  String? get id;
  @override
  @JsonKey(name: "created_at")
  DateTime? get createdAt;
  @override
  @JsonKey(name: "updated_at")
  DateTime? get updatedAt;
  @override
  @JsonKey(name: "width")
  int? get width;
  @override
  @JsonKey(name: "height")
  int? get height;
  @override
  @JsonKey(name: "color")
  String? get color;
  @override
  @JsonKey(name: "blur_hash")
  String? get blurHash;
  @override
  @JsonKey(name: "likes")
  int? get likes;
  @override
  @JsonKey(name: "liked_by_user")
  bool? get likedByUser;
  @override
  @JsonKey(name: "description")
  String? get description;
  @override
  @JsonKey(name: "user")
  User? get user;
  @override
  @JsonKey(name: "current_user_collections")
  List<CurrentUserCollection>? get currentUserCollections;
  @override
  @JsonKey(name: "urls")
  Urls? get urls;
  @override
  @JsonKey(name: "links")
  ImageResponseLinks? get links;

  /// Create a copy of ImageResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ImageResponseImplCopyWith<_$ImageResponseImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

CurrentUserCollection _$CurrentUserCollectionFromJson(
    Map<String, dynamic> json) {
  return _CurrentUserCollection.fromJson(json);
}

/// @nodoc
mixin _$CurrentUserCollection {
  @JsonKey(name: "id")
  int? get id => throw _privateConstructorUsedError;
  @JsonKey(name: "title")
  String? get title => throw _privateConstructorUsedError;
  @JsonKey(name: "published_at")
  DateTime? get publishedAt => throw _privateConstructorUsedError;
  @JsonKey(name: "last_collected_at")
  DateTime? get lastCollectedAt => throw _privateConstructorUsedError;
  @JsonKey(name: "updated_at")
  DateTime? get updatedAt => throw _privateConstructorUsedError;
  @JsonKey(name: "cover_photo")
  dynamic get coverPhoto => throw _privateConstructorUsedError;
  @JsonKey(name: "user")
  dynamic get user => throw _privateConstructorUsedError;

  /// Serializes this CurrentUserCollection to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of CurrentUserCollection
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CurrentUserCollectionCopyWith<CurrentUserCollection> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CurrentUserCollectionCopyWith<$Res> {
  factory $CurrentUserCollectionCopyWith(CurrentUserCollection value,
          $Res Function(CurrentUserCollection) then) =
      _$CurrentUserCollectionCopyWithImpl<$Res, CurrentUserCollection>;
  @useResult
  $Res call(
      {@JsonKey(name: "id") int? id,
      @JsonKey(name: "title") String? title,
      @JsonKey(name: "published_at") DateTime? publishedAt,
      @JsonKey(name: "last_collected_at") DateTime? lastCollectedAt,
      @JsonKey(name: "updated_at") DateTime? updatedAt,
      @JsonKey(name: "cover_photo") dynamic coverPhoto,
      @JsonKey(name: "user") dynamic user});
}

/// @nodoc
class _$CurrentUserCollectionCopyWithImpl<$Res,
        $Val extends CurrentUserCollection>
    implements $CurrentUserCollectionCopyWith<$Res> {
  _$CurrentUserCollectionCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CurrentUserCollection
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? title = freezed,
    Object? publishedAt = freezed,
    Object? lastCollectedAt = freezed,
    Object? updatedAt = freezed,
    Object? coverPhoto = freezed,
    Object? user = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      publishedAt: freezed == publishedAt
          ? _value.publishedAt
          : publishedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      lastCollectedAt: freezed == lastCollectedAt
          ? _value.lastCollectedAt
          : lastCollectedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      coverPhoto: freezed == coverPhoto
          ? _value.coverPhoto
          : coverPhoto // ignore: cast_nullable_to_non_nullable
              as dynamic,
      user: freezed == user
          ? _value.user
          : user // ignore: cast_nullable_to_non_nullable
              as dynamic,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CurrentUserCollectionImplCopyWith<$Res>
    implements $CurrentUserCollectionCopyWith<$Res> {
  factory _$$CurrentUserCollectionImplCopyWith(
          _$CurrentUserCollectionImpl value,
          $Res Function(_$CurrentUserCollectionImpl) then) =
      __$$CurrentUserCollectionImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: "id") int? id,
      @JsonKey(name: "title") String? title,
      @JsonKey(name: "published_at") DateTime? publishedAt,
      @JsonKey(name: "last_collected_at") DateTime? lastCollectedAt,
      @JsonKey(name: "updated_at") DateTime? updatedAt,
      @JsonKey(name: "cover_photo") dynamic coverPhoto,
      @JsonKey(name: "user") dynamic user});
}

/// @nodoc
class __$$CurrentUserCollectionImplCopyWithImpl<$Res>
    extends _$CurrentUserCollectionCopyWithImpl<$Res,
        _$CurrentUserCollectionImpl>
    implements _$$CurrentUserCollectionImplCopyWith<$Res> {
  __$$CurrentUserCollectionImplCopyWithImpl(_$CurrentUserCollectionImpl _value,
      $Res Function(_$CurrentUserCollectionImpl) _then)
      : super(_value, _then);

  /// Create a copy of CurrentUserCollection
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? title = freezed,
    Object? publishedAt = freezed,
    Object? lastCollectedAt = freezed,
    Object? updatedAt = freezed,
    Object? coverPhoto = freezed,
    Object? user = freezed,
  }) {
    return _then(_$CurrentUserCollectionImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      publishedAt: freezed == publishedAt
          ? _value.publishedAt
          : publishedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      lastCollectedAt: freezed == lastCollectedAt
          ? _value.lastCollectedAt
          : lastCollectedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      coverPhoto: freezed == coverPhoto
          ? _value.coverPhoto
          : coverPhoto // ignore: cast_nullable_to_non_nullable
              as dynamic,
      user: freezed == user
          ? _value.user
          : user // ignore: cast_nullable_to_non_nullable
              as dynamic,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CurrentUserCollectionImpl implements _CurrentUserCollection {
  const _$CurrentUserCollectionImpl(
      {@JsonKey(name: "id") this.id,
      @JsonKey(name: "title") this.title,
      @JsonKey(name: "published_at") this.publishedAt,
      @JsonKey(name: "last_collected_at") this.lastCollectedAt,
      @JsonKey(name: "updated_at") this.updatedAt,
      @JsonKey(name: "cover_photo") this.coverPhoto,
      @JsonKey(name: "user") this.user});

  factory _$CurrentUserCollectionImpl.fromJson(Map<String, dynamic> json) =>
      _$$CurrentUserCollectionImplFromJson(json);

  @override
  @JsonKey(name: "id")
  final int? id;
  @override
  @JsonKey(name: "title")
  final String? title;
  @override
  @JsonKey(name: "published_at")
  final DateTime? publishedAt;
  @override
  @JsonKey(name: "last_collected_at")
  final DateTime? lastCollectedAt;
  @override
  @JsonKey(name: "updated_at")
  final DateTime? updatedAt;
  @override
  @JsonKey(name: "cover_photo")
  final dynamic coverPhoto;
  @override
  @JsonKey(name: "user")
  final dynamic user;

  @override
  String toString() {
    return 'CurrentUserCollection(id: $id, title: $title, publishedAt: $publishedAt, lastCollectedAt: $lastCollectedAt, updatedAt: $updatedAt, coverPhoto: $coverPhoto, user: $user)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CurrentUserCollectionImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.publishedAt, publishedAt) ||
                other.publishedAt == publishedAt) &&
            (identical(other.lastCollectedAt, lastCollectedAt) ||
                other.lastCollectedAt == lastCollectedAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt) &&
            const DeepCollectionEquality()
                .equals(other.coverPhoto, coverPhoto) &&
            const DeepCollectionEquality().equals(other.user, user));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      title,
      publishedAt,
      lastCollectedAt,
      updatedAt,
      const DeepCollectionEquality().hash(coverPhoto),
      const DeepCollectionEquality().hash(user));

  /// Create a copy of CurrentUserCollection
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CurrentUserCollectionImplCopyWith<_$CurrentUserCollectionImpl>
      get copyWith => __$$CurrentUserCollectionImplCopyWithImpl<
          _$CurrentUserCollectionImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CurrentUserCollectionImplToJson(
      this,
    );
  }
}

abstract class _CurrentUserCollection implements CurrentUserCollection {
  const factory _CurrentUserCollection(
      {@JsonKey(name: "id") final int? id,
      @JsonKey(name: "title") final String? title,
      @JsonKey(name: "published_at") final DateTime? publishedAt,
      @JsonKey(name: "last_collected_at") final DateTime? lastCollectedAt,
      @JsonKey(name: "updated_at") final DateTime? updatedAt,
      @JsonKey(name: "cover_photo") final dynamic coverPhoto,
      @JsonKey(name: "user") final dynamic user}) = _$CurrentUserCollectionImpl;

  factory _CurrentUserCollection.fromJson(Map<String, dynamic> json) =
      _$CurrentUserCollectionImpl.fromJson;

  @override
  @JsonKey(name: "id")
  int? get id;
  @override
  @JsonKey(name: "title")
  String? get title;
  @override
  @JsonKey(name: "published_at")
  DateTime? get publishedAt;
  @override
  @JsonKey(name: "last_collected_at")
  DateTime? get lastCollectedAt;
  @override
  @JsonKey(name: "updated_at")
  DateTime? get updatedAt;
  @override
  @JsonKey(name: "cover_photo")
  dynamic get coverPhoto;
  @override
  @JsonKey(name: "user")
  dynamic get user;

  /// Create a copy of CurrentUserCollection
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CurrentUserCollectionImplCopyWith<_$CurrentUserCollectionImpl>
      get copyWith => throw _privateConstructorUsedError;
}

ImageResponseLinks _$ImageResponseLinksFromJson(Map<String, dynamic> json) {
  return _ImageResponseLinks.fromJson(json);
}

/// @nodoc
mixin _$ImageResponseLinks {
  @JsonKey(name: "self")
  String? get self => throw _privateConstructorUsedError;
  @JsonKey(name: "html")
  String? get html => throw _privateConstructorUsedError;
  @JsonKey(name: "download")
  String? get download => throw _privateConstructorUsedError;
  @JsonKey(name: "download_location")
  String? get downloadLocation => throw _privateConstructorUsedError;

  /// Serializes this ImageResponseLinks to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ImageResponseLinks
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ImageResponseLinksCopyWith<ImageResponseLinks> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ImageResponseLinksCopyWith<$Res> {
  factory $ImageResponseLinksCopyWith(
          ImageResponseLinks value, $Res Function(ImageResponseLinks) then) =
      _$ImageResponseLinksCopyWithImpl<$Res, ImageResponseLinks>;
  @useResult
  $Res call(
      {@JsonKey(name: "self") String? self,
      @JsonKey(name: "html") String? html,
      @JsonKey(name: "download") String? download,
      @JsonKey(name: "download_location") String? downloadLocation});
}

/// @nodoc
class _$ImageResponseLinksCopyWithImpl<$Res, $Val extends ImageResponseLinks>
    implements $ImageResponseLinksCopyWith<$Res> {
  _$ImageResponseLinksCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ImageResponseLinks
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? self = freezed,
    Object? html = freezed,
    Object? download = freezed,
    Object? downloadLocation = freezed,
  }) {
    return _then(_value.copyWith(
      self: freezed == self
          ? _value.self
          : self // ignore: cast_nullable_to_non_nullable
              as String?,
      html: freezed == html
          ? _value.html
          : html // ignore: cast_nullable_to_non_nullable
              as String?,
      download: freezed == download
          ? _value.download
          : download // ignore: cast_nullable_to_non_nullable
              as String?,
      downloadLocation: freezed == downloadLocation
          ? _value.downloadLocation
          : downloadLocation // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ImageResponseLinksImplCopyWith<$Res>
    implements $ImageResponseLinksCopyWith<$Res> {
  factory _$$ImageResponseLinksImplCopyWith(_$ImageResponseLinksImpl value,
          $Res Function(_$ImageResponseLinksImpl) then) =
      __$$ImageResponseLinksImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: "self") String? self,
      @JsonKey(name: "html") String? html,
      @JsonKey(name: "download") String? download,
      @JsonKey(name: "download_location") String? downloadLocation});
}

/// @nodoc
class __$$ImageResponseLinksImplCopyWithImpl<$Res>
    extends _$ImageResponseLinksCopyWithImpl<$Res, _$ImageResponseLinksImpl>
    implements _$$ImageResponseLinksImplCopyWith<$Res> {
  __$$ImageResponseLinksImplCopyWithImpl(_$ImageResponseLinksImpl _value,
      $Res Function(_$ImageResponseLinksImpl) _then)
      : super(_value, _then);

  /// Create a copy of ImageResponseLinks
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? self = freezed,
    Object? html = freezed,
    Object? download = freezed,
    Object? downloadLocation = freezed,
  }) {
    return _then(_$ImageResponseLinksImpl(
      self: freezed == self
          ? _value.self
          : self // ignore: cast_nullable_to_non_nullable
              as String?,
      html: freezed == html
          ? _value.html
          : html // ignore: cast_nullable_to_non_nullable
              as String?,
      download: freezed == download
          ? _value.download
          : download // ignore: cast_nullable_to_non_nullable
              as String?,
      downloadLocation: freezed == downloadLocation
          ? _value.downloadLocation
          : downloadLocation // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ImageResponseLinksImpl implements _ImageResponseLinks {
  const _$ImageResponseLinksImpl(
      {@JsonKey(name: "self") this.self,
      @JsonKey(name: "html") this.html,
      @JsonKey(name: "download") this.download,
      @JsonKey(name: "download_location") this.downloadLocation});

  factory _$ImageResponseLinksImpl.fromJson(Map<String, dynamic> json) =>
      _$$ImageResponseLinksImplFromJson(json);

  @override
  @JsonKey(name: "self")
  final String? self;
  @override
  @JsonKey(name: "html")
  final String? html;
  @override
  @JsonKey(name: "download")
  final String? download;
  @override
  @JsonKey(name: "download_location")
  final String? downloadLocation;

  @override
  String toString() {
    return 'ImageResponseLinks(self: $self, html: $html, download: $download, downloadLocation: $downloadLocation)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ImageResponseLinksImpl &&
            (identical(other.self, self) || other.self == self) &&
            (identical(other.html, html) || other.html == html) &&
            (identical(other.download, download) ||
                other.download == download) &&
            (identical(other.downloadLocation, downloadLocation) ||
                other.downloadLocation == downloadLocation));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, self, html, download, downloadLocation);

  /// Create a copy of ImageResponseLinks
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ImageResponseLinksImplCopyWith<_$ImageResponseLinksImpl> get copyWith =>
      __$$ImageResponseLinksImplCopyWithImpl<_$ImageResponseLinksImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ImageResponseLinksImplToJson(
      this,
    );
  }
}

abstract class _ImageResponseLinks implements ImageResponseLinks {
  const factory _ImageResponseLinks(
          {@JsonKey(name: "self") final String? self,
          @JsonKey(name: "html") final String? html,
          @JsonKey(name: "download") final String? download,
          @JsonKey(name: "download_location") final String? downloadLocation}) =
      _$ImageResponseLinksImpl;

  factory _ImageResponseLinks.fromJson(Map<String, dynamic> json) =
      _$ImageResponseLinksImpl.fromJson;

  @override
  @JsonKey(name: "self")
  String? get self;
  @override
  @JsonKey(name: "html")
  String? get html;
  @override
  @JsonKey(name: "download")
  String? get download;
  @override
  @JsonKey(name: "download_location")
  String? get downloadLocation;

  /// Create a copy of ImageResponseLinks
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ImageResponseLinksImplCopyWith<_$ImageResponseLinksImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

Urls _$UrlsFromJson(Map<String, dynamic> json) {
  return _Urls.fromJson(json);
}

/// @nodoc
mixin _$Urls {
  @JsonKey(name: "raw")
  String? get raw => throw _privateConstructorUsedError;
  @JsonKey(name: "full")
  String? get full => throw _privateConstructorUsedError;
  @JsonKey(name: "regular")
  String? get regular => throw _privateConstructorUsedError;
  @JsonKey(name: "small")
  String? get small => throw _privateConstructorUsedError;
  @JsonKey(name: "thumb")
  String? get thumb => throw _privateConstructorUsedError;

  /// Serializes this Urls to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of Urls
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $UrlsCopyWith<Urls> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UrlsCopyWith<$Res> {
  factory $UrlsCopyWith(Urls value, $Res Function(Urls) then) =
      _$UrlsCopyWithImpl<$Res, Urls>;
  @useResult
  $Res call(
      {@JsonKey(name: "raw") String? raw,
      @JsonKey(name: "full") String? full,
      @JsonKey(name: "regular") String? regular,
      @JsonKey(name: "small") String? small,
      @JsonKey(name: "thumb") String? thumb});
}

/// @nodoc
class _$UrlsCopyWithImpl<$Res, $Val extends Urls>
    implements $UrlsCopyWith<$Res> {
  _$UrlsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of Urls
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? raw = freezed,
    Object? full = freezed,
    Object? regular = freezed,
    Object? small = freezed,
    Object? thumb = freezed,
  }) {
    return _then(_value.copyWith(
      raw: freezed == raw
          ? _value.raw
          : raw // ignore: cast_nullable_to_non_nullable
              as String?,
      full: freezed == full
          ? _value.full
          : full // ignore: cast_nullable_to_non_nullable
              as String?,
      regular: freezed == regular
          ? _value.regular
          : regular // ignore: cast_nullable_to_non_nullable
              as String?,
      small: freezed == small
          ? _value.small
          : small // ignore: cast_nullable_to_non_nullable
              as String?,
      thumb: freezed == thumb
          ? _value.thumb
          : thumb // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$UrlsImplCopyWith<$Res> implements $UrlsCopyWith<$Res> {
  factory _$$UrlsImplCopyWith(
          _$UrlsImpl value, $Res Function(_$UrlsImpl) then) =
      __$$UrlsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: "raw") String? raw,
      @JsonKey(name: "full") String? full,
      @JsonKey(name: "regular") String? regular,
      @JsonKey(name: "small") String? small,
      @JsonKey(name: "thumb") String? thumb});
}

/// @nodoc
class __$$UrlsImplCopyWithImpl<$Res>
    extends _$UrlsCopyWithImpl<$Res, _$UrlsImpl>
    implements _$$UrlsImplCopyWith<$Res> {
  __$$UrlsImplCopyWithImpl(_$UrlsImpl _value, $Res Function(_$UrlsImpl) _then)
      : super(_value, _then);

  /// Create a copy of Urls
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? raw = freezed,
    Object? full = freezed,
    Object? regular = freezed,
    Object? small = freezed,
    Object? thumb = freezed,
  }) {
    return _then(_$UrlsImpl(
      raw: freezed == raw
          ? _value.raw
          : raw // ignore: cast_nullable_to_non_nullable
              as String?,
      full: freezed == full
          ? _value.full
          : full // ignore: cast_nullable_to_non_nullable
              as String?,
      regular: freezed == regular
          ? _value.regular
          : regular // ignore: cast_nullable_to_non_nullable
              as String?,
      small: freezed == small
          ? _value.small
          : small // ignore: cast_nullable_to_non_nullable
              as String?,
      thumb: freezed == thumb
          ? _value.thumb
          : thumb // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$UrlsImpl implements _Urls {
  const _$UrlsImpl(
      {@JsonKey(name: "raw") this.raw,
      @JsonKey(name: "full") this.full,
      @JsonKey(name: "regular") this.regular,
      @JsonKey(name: "small") this.small,
      @JsonKey(name: "thumb") this.thumb});

  factory _$UrlsImpl.fromJson(Map<String, dynamic> json) =>
      _$$UrlsImplFromJson(json);

  @override
  @JsonKey(name: "raw")
  final String? raw;
  @override
  @JsonKey(name: "full")
  final String? full;
  @override
  @JsonKey(name: "regular")
  final String? regular;
  @override
  @JsonKey(name: "small")
  final String? small;
  @override
  @JsonKey(name: "thumb")
  final String? thumb;

  @override
  String toString() {
    return 'Urls(raw: $raw, full: $full, regular: $regular, small: $small, thumb: $thumb)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UrlsImpl &&
            (identical(other.raw, raw) || other.raw == raw) &&
            (identical(other.full, full) || other.full == full) &&
            (identical(other.regular, regular) || other.regular == regular) &&
            (identical(other.small, small) || other.small == small) &&
            (identical(other.thumb, thumb) || other.thumb == thumb));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, raw, full, regular, small, thumb);

  /// Create a copy of Urls
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UrlsImplCopyWith<_$UrlsImpl> get copyWith =>
      __$$UrlsImplCopyWithImpl<_$UrlsImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$UrlsImplToJson(
      this,
    );
  }
}

abstract class _Urls implements Urls {
  const factory _Urls(
      {@JsonKey(name: "raw") final String? raw,
      @JsonKey(name: "full") final String? full,
      @JsonKey(name: "regular") final String? regular,
      @JsonKey(name: "small") final String? small,
      @JsonKey(name: "thumb") final String? thumb}) = _$UrlsImpl;

  factory _Urls.fromJson(Map<String, dynamic> json) = _$UrlsImpl.fromJson;

  @override
  @JsonKey(name: "raw")
  String? get raw;
  @override
  @JsonKey(name: "full")
  String? get full;
  @override
  @JsonKey(name: "regular")
  String? get regular;
  @override
  @JsonKey(name: "small")
  String? get small;
  @override
  @JsonKey(name: "thumb")
  String? get thumb;

  /// Create a copy of Urls
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UrlsImplCopyWith<_$UrlsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

User _$UserFromJson(Map<String, dynamic> json) {
  return _User.fromJson(json);
}

/// @nodoc
mixin _$User {
  @JsonKey(name: "id")
  String? get id => throw _privateConstructorUsedError;
  @JsonKey(name: "username")
  String? get username => throw _privateConstructorUsedError;
  @JsonKey(name: "name")
  String? get name => throw _privateConstructorUsedError;
  @JsonKey(name: "portfolio_url")
  String? get portfolioUrl => throw _privateConstructorUsedError;
  @JsonKey(name: "bio")
  String? get bio => throw _privateConstructorUsedError;
  @JsonKey(name: "location")
  String? get location => throw _privateConstructorUsedError;
  @JsonKey(name: "total_likes")
  int? get totalLikes => throw _privateConstructorUsedError;
  @JsonKey(name: "total_photos")
  int? get totalPhotos => throw _privateConstructorUsedError;
  @JsonKey(name: "total_collections")
  int? get totalCollections => throw _privateConstructorUsedError;
  @JsonKey(name: "instagram_username")
  String? get instagramUsername => throw _privateConstructorUsedError;
  @JsonKey(name: "twitter_username")
  String? get twitterUsername => throw _privateConstructorUsedError;
  @JsonKey(name: "profile_image")
  ProfileImage? get profileImage => throw _privateConstructorUsedError;
  @JsonKey(name: "links")
  UserLinks? get links => throw _privateConstructorUsedError;

  /// Serializes this User to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of User
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $UserCopyWith<User> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UserCopyWith<$Res> {
  factory $UserCopyWith(User value, $Res Function(User) then) =
      _$UserCopyWithImpl<$Res, User>;
  @useResult
  $Res call(
      {@JsonKey(name: "id") String? id,
      @JsonKey(name: "username") String? username,
      @JsonKey(name: "name") String? name,
      @JsonKey(name: "portfolio_url") String? portfolioUrl,
      @JsonKey(name: "bio") String? bio,
      @JsonKey(name: "location") String? location,
      @JsonKey(name: "total_likes") int? totalLikes,
      @JsonKey(name: "total_photos") int? totalPhotos,
      @JsonKey(name: "total_collections") int? totalCollections,
      @JsonKey(name: "instagram_username") String? instagramUsername,
      @JsonKey(name: "twitter_username") String? twitterUsername,
      @JsonKey(name: "profile_image") ProfileImage? profileImage,
      @JsonKey(name: "links") UserLinks? links});

  $ProfileImageCopyWith<$Res>? get profileImage;
  $UserLinksCopyWith<$Res>? get links;
}

/// @nodoc
class _$UserCopyWithImpl<$Res, $Val extends User>
    implements $UserCopyWith<$Res> {
  _$UserCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of User
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? username = freezed,
    Object? name = freezed,
    Object? portfolioUrl = freezed,
    Object? bio = freezed,
    Object? location = freezed,
    Object? totalLikes = freezed,
    Object? totalPhotos = freezed,
    Object? totalCollections = freezed,
    Object? instagramUsername = freezed,
    Object? twitterUsername = freezed,
    Object? profileImage = freezed,
    Object? links = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      username: freezed == username
          ? _value.username
          : username // ignore: cast_nullable_to_non_nullable
              as String?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      portfolioUrl: freezed == portfolioUrl
          ? _value.portfolioUrl
          : portfolioUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      bio: freezed == bio
          ? _value.bio
          : bio // ignore: cast_nullable_to_non_nullable
              as String?,
      location: freezed == location
          ? _value.location
          : location // ignore: cast_nullable_to_non_nullable
              as String?,
      totalLikes: freezed == totalLikes
          ? _value.totalLikes
          : totalLikes // ignore: cast_nullable_to_non_nullable
              as int?,
      totalPhotos: freezed == totalPhotos
          ? _value.totalPhotos
          : totalPhotos // ignore: cast_nullable_to_non_nullable
              as int?,
      totalCollections: freezed == totalCollections
          ? _value.totalCollections
          : totalCollections // ignore: cast_nullable_to_non_nullable
              as int?,
      instagramUsername: freezed == instagramUsername
          ? _value.instagramUsername
          : instagramUsername // ignore: cast_nullable_to_non_nullable
              as String?,
      twitterUsername: freezed == twitterUsername
          ? _value.twitterUsername
          : twitterUsername // ignore: cast_nullable_to_non_nullable
              as String?,
      profileImage: freezed == profileImage
          ? _value.profileImage
          : profileImage // ignore: cast_nullable_to_non_nullable
              as ProfileImage?,
      links: freezed == links
          ? _value.links
          : links // ignore: cast_nullable_to_non_nullable
              as UserLinks?,
    ) as $Val);
  }

  /// Create a copy of User
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ProfileImageCopyWith<$Res>? get profileImage {
    if (_value.profileImage == null) {
      return null;
    }

    return $ProfileImageCopyWith<$Res>(_value.profileImage!, (value) {
      return _then(_value.copyWith(profileImage: value) as $Val);
    });
  }

  /// Create a copy of User
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $UserLinksCopyWith<$Res>? get links {
    if (_value.links == null) {
      return null;
    }

    return $UserLinksCopyWith<$Res>(_value.links!, (value) {
      return _then(_value.copyWith(links: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$UserImplCopyWith<$Res> implements $UserCopyWith<$Res> {
  factory _$$UserImplCopyWith(
          _$UserImpl value, $Res Function(_$UserImpl) then) =
      __$$UserImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: "id") String? id,
      @JsonKey(name: "username") String? username,
      @JsonKey(name: "name") String? name,
      @JsonKey(name: "portfolio_url") String? portfolioUrl,
      @JsonKey(name: "bio") String? bio,
      @JsonKey(name: "location") String? location,
      @JsonKey(name: "total_likes") int? totalLikes,
      @JsonKey(name: "total_photos") int? totalPhotos,
      @JsonKey(name: "total_collections") int? totalCollections,
      @JsonKey(name: "instagram_username") String? instagramUsername,
      @JsonKey(name: "twitter_username") String? twitterUsername,
      @JsonKey(name: "profile_image") ProfileImage? profileImage,
      @JsonKey(name: "links") UserLinks? links});

  @override
  $ProfileImageCopyWith<$Res>? get profileImage;
  @override
  $UserLinksCopyWith<$Res>? get links;
}

/// @nodoc
class __$$UserImplCopyWithImpl<$Res>
    extends _$UserCopyWithImpl<$Res, _$UserImpl>
    implements _$$UserImplCopyWith<$Res> {
  __$$UserImplCopyWithImpl(_$UserImpl _value, $Res Function(_$UserImpl) _then)
      : super(_value, _then);

  /// Create a copy of User
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? username = freezed,
    Object? name = freezed,
    Object? portfolioUrl = freezed,
    Object? bio = freezed,
    Object? location = freezed,
    Object? totalLikes = freezed,
    Object? totalPhotos = freezed,
    Object? totalCollections = freezed,
    Object? instagramUsername = freezed,
    Object? twitterUsername = freezed,
    Object? profileImage = freezed,
    Object? links = freezed,
  }) {
    return _then(_$UserImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      username: freezed == username
          ? _value.username
          : username // ignore: cast_nullable_to_non_nullable
              as String?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      portfolioUrl: freezed == portfolioUrl
          ? _value.portfolioUrl
          : portfolioUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      bio: freezed == bio
          ? _value.bio
          : bio // ignore: cast_nullable_to_non_nullable
              as String?,
      location: freezed == location
          ? _value.location
          : location // ignore: cast_nullable_to_non_nullable
              as String?,
      totalLikes: freezed == totalLikes
          ? _value.totalLikes
          : totalLikes // ignore: cast_nullable_to_non_nullable
              as int?,
      totalPhotos: freezed == totalPhotos
          ? _value.totalPhotos
          : totalPhotos // ignore: cast_nullable_to_non_nullable
              as int?,
      totalCollections: freezed == totalCollections
          ? _value.totalCollections
          : totalCollections // ignore: cast_nullable_to_non_nullable
              as int?,
      instagramUsername: freezed == instagramUsername
          ? _value.instagramUsername
          : instagramUsername // ignore: cast_nullable_to_non_nullable
              as String?,
      twitterUsername: freezed == twitterUsername
          ? _value.twitterUsername
          : twitterUsername // ignore: cast_nullable_to_non_nullable
              as String?,
      profileImage: freezed == profileImage
          ? _value.profileImage
          : profileImage // ignore: cast_nullable_to_non_nullable
              as ProfileImage?,
      links: freezed == links
          ? _value.links
          : links // ignore: cast_nullable_to_non_nullable
              as UserLinks?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$UserImpl implements _User {
  const _$UserImpl(
      {@JsonKey(name: "id") this.id,
      @JsonKey(name: "username") this.username,
      @JsonKey(name: "name") this.name,
      @JsonKey(name: "portfolio_url") this.portfolioUrl,
      @JsonKey(name: "bio") this.bio,
      @JsonKey(name: "location") this.location,
      @JsonKey(name: "total_likes") this.totalLikes,
      @JsonKey(name: "total_photos") this.totalPhotos,
      @JsonKey(name: "total_collections") this.totalCollections,
      @JsonKey(name: "instagram_username") this.instagramUsername,
      @JsonKey(name: "twitter_username") this.twitterUsername,
      @JsonKey(name: "profile_image") this.profileImage,
      @JsonKey(name: "links") this.links});

  factory _$UserImpl.fromJson(Map<String, dynamic> json) =>
      _$$UserImplFromJson(json);

  @override
  @JsonKey(name: "id")
  final String? id;
  @override
  @JsonKey(name: "username")
  final String? username;
  @override
  @JsonKey(name: "name")
  final String? name;
  @override
  @JsonKey(name: "portfolio_url")
  final String? portfolioUrl;
  @override
  @JsonKey(name: "bio")
  final String? bio;
  @override
  @JsonKey(name: "location")
  final String? location;
  @override
  @JsonKey(name: "total_likes")
  final int? totalLikes;
  @override
  @JsonKey(name: "total_photos")
  final int? totalPhotos;
  @override
  @JsonKey(name: "total_collections")
  final int? totalCollections;
  @override
  @JsonKey(name: "instagram_username")
  final String? instagramUsername;
  @override
  @JsonKey(name: "twitter_username")
  final String? twitterUsername;
  @override
  @JsonKey(name: "profile_image")
  final ProfileImage? profileImage;
  @override
  @JsonKey(name: "links")
  final UserLinks? links;

  @override
  String toString() {
    return 'User(id: $id, username: $username, name: $name, portfolioUrl: $portfolioUrl, bio: $bio, location: $location, totalLikes: $totalLikes, totalPhotos: $totalPhotos, totalCollections: $totalCollections, instagramUsername: $instagramUsername, twitterUsername: $twitterUsername, profileImage: $profileImage, links: $links)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UserImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.username, username) ||
                other.username == username) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.portfolioUrl, portfolioUrl) ||
                other.portfolioUrl == portfolioUrl) &&
            (identical(other.bio, bio) || other.bio == bio) &&
            (identical(other.location, location) ||
                other.location == location) &&
            (identical(other.totalLikes, totalLikes) ||
                other.totalLikes == totalLikes) &&
            (identical(other.totalPhotos, totalPhotos) ||
                other.totalPhotos == totalPhotos) &&
            (identical(other.totalCollections, totalCollections) ||
                other.totalCollections == totalCollections) &&
            (identical(other.instagramUsername, instagramUsername) ||
                other.instagramUsername == instagramUsername) &&
            (identical(other.twitterUsername, twitterUsername) ||
                other.twitterUsername == twitterUsername) &&
            (identical(other.profileImage, profileImage) ||
                other.profileImage == profileImage) &&
            (identical(other.links, links) || other.links == links));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      username,
      name,
      portfolioUrl,
      bio,
      location,
      totalLikes,
      totalPhotos,
      totalCollections,
      instagramUsername,
      twitterUsername,
      profileImage,
      links);

  /// Create a copy of User
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UserImplCopyWith<_$UserImpl> get copyWith =>
      __$$UserImplCopyWithImpl<_$UserImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$UserImplToJson(
      this,
    );
  }
}

abstract class _User implements User {
  const factory _User(
      {@JsonKey(name: "id") final String? id,
      @JsonKey(name: "username") final String? username,
      @JsonKey(name: "name") final String? name,
      @JsonKey(name: "portfolio_url") final String? portfolioUrl,
      @JsonKey(name: "bio") final String? bio,
      @JsonKey(name: "location") final String? location,
      @JsonKey(name: "total_likes") final int? totalLikes,
      @JsonKey(name: "total_photos") final int? totalPhotos,
      @JsonKey(name: "total_collections") final int? totalCollections,
      @JsonKey(name: "instagram_username") final String? instagramUsername,
      @JsonKey(name: "twitter_username") final String? twitterUsername,
      @JsonKey(name: "profile_image") final ProfileImage? profileImage,
      @JsonKey(name: "links") final UserLinks? links}) = _$UserImpl;

  factory _User.fromJson(Map<String, dynamic> json) = _$UserImpl.fromJson;

  @override
  @JsonKey(name: "id")
  String? get id;
  @override
  @JsonKey(name: "username")
  String? get username;
  @override
  @JsonKey(name: "name")
  String? get name;
  @override
  @JsonKey(name: "portfolio_url")
  String? get portfolioUrl;
  @override
  @JsonKey(name: "bio")
  String? get bio;
  @override
  @JsonKey(name: "location")
  String? get location;
  @override
  @JsonKey(name: "total_likes")
  int? get totalLikes;
  @override
  @JsonKey(name: "total_photos")
  int? get totalPhotos;
  @override
  @JsonKey(name: "total_collections")
  int? get totalCollections;
  @override
  @JsonKey(name: "instagram_username")
  String? get instagramUsername;
  @override
  @JsonKey(name: "twitter_username")
  String? get twitterUsername;
  @override
  @JsonKey(name: "profile_image")
  ProfileImage? get profileImage;
  @override
  @JsonKey(name: "links")
  UserLinks? get links;

  /// Create a copy of User
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UserImplCopyWith<_$UserImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

UserLinks _$UserLinksFromJson(Map<String, dynamic> json) {
  return _UserLinks.fromJson(json);
}

/// @nodoc
mixin _$UserLinks {
  @JsonKey(name: "self")
  String? get self => throw _privateConstructorUsedError;
  @JsonKey(name: "html")
  String? get html => throw _privateConstructorUsedError;
  @JsonKey(name: "photos")
  String? get photos => throw _privateConstructorUsedError;
  @JsonKey(name: "likes")
  String? get likes => throw _privateConstructorUsedError;
  @JsonKey(name: "portfolio")
  String? get portfolio => throw _privateConstructorUsedError;

  /// Serializes this UserLinks to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of UserLinks
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $UserLinksCopyWith<UserLinks> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UserLinksCopyWith<$Res> {
  factory $UserLinksCopyWith(UserLinks value, $Res Function(UserLinks) then) =
      _$UserLinksCopyWithImpl<$Res, UserLinks>;
  @useResult
  $Res call(
      {@JsonKey(name: "self") String? self,
      @JsonKey(name: "html") String? html,
      @JsonKey(name: "photos") String? photos,
      @JsonKey(name: "likes") String? likes,
      @JsonKey(name: "portfolio") String? portfolio});
}

/// @nodoc
class _$UserLinksCopyWithImpl<$Res, $Val extends UserLinks>
    implements $UserLinksCopyWith<$Res> {
  _$UserLinksCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of UserLinks
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? self = freezed,
    Object? html = freezed,
    Object? photos = freezed,
    Object? likes = freezed,
    Object? portfolio = freezed,
  }) {
    return _then(_value.copyWith(
      self: freezed == self
          ? _value.self
          : self // ignore: cast_nullable_to_non_nullable
              as String?,
      html: freezed == html
          ? _value.html
          : html // ignore: cast_nullable_to_non_nullable
              as String?,
      photos: freezed == photos
          ? _value.photos
          : photos // ignore: cast_nullable_to_non_nullable
              as String?,
      likes: freezed == likes
          ? _value.likes
          : likes // ignore: cast_nullable_to_non_nullable
              as String?,
      portfolio: freezed == portfolio
          ? _value.portfolio
          : portfolio // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$UserLinksImplCopyWith<$Res>
    implements $UserLinksCopyWith<$Res> {
  factory _$$UserLinksImplCopyWith(
          _$UserLinksImpl value, $Res Function(_$UserLinksImpl) then) =
      __$$UserLinksImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: "self") String? self,
      @JsonKey(name: "html") String? html,
      @JsonKey(name: "photos") String? photos,
      @JsonKey(name: "likes") String? likes,
      @JsonKey(name: "portfolio") String? portfolio});
}

/// @nodoc
class __$$UserLinksImplCopyWithImpl<$Res>
    extends _$UserLinksCopyWithImpl<$Res, _$UserLinksImpl>
    implements _$$UserLinksImplCopyWith<$Res> {
  __$$UserLinksImplCopyWithImpl(
      _$UserLinksImpl _value, $Res Function(_$UserLinksImpl) _then)
      : super(_value, _then);

  /// Create a copy of UserLinks
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? self = freezed,
    Object? html = freezed,
    Object? photos = freezed,
    Object? likes = freezed,
    Object? portfolio = freezed,
  }) {
    return _then(_$UserLinksImpl(
      self: freezed == self
          ? _value.self
          : self // ignore: cast_nullable_to_non_nullable
              as String?,
      html: freezed == html
          ? _value.html
          : html // ignore: cast_nullable_to_non_nullable
              as String?,
      photos: freezed == photos
          ? _value.photos
          : photos // ignore: cast_nullable_to_non_nullable
              as String?,
      likes: freezed == likes
          ? _value.likes
          : likes // ignore: cast_nullable_to_non_nullable
              as String?,
      portfolio: freezed == portfolio
          ? _value.portfolio
          : portfolio // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$UserLinksImpl implements _UserLinks {
  const _$UserLinksImpl(
      {@JsonKey(name: "self") this.self,
      @JsonKey(name: "html") this.html,
      @JsonKey(name: "photos") this.photos,
      @JsonKey(name: "likes") this.likes,
      @JsonKey(name: "portfolio") this.portfolio});

  factory _$UserLinksImpl.fromJson(Map<String, dynamic> json) =>
      _$$UserLinksImplFromJson(json);

  @override
  @JsonKey(name: "self")
  final String? self;
  @override
  @JsonKey(name: "html")
  final String? html;
  @override
  @JsonKey(name: "photos")
  final String? photos;
  @override
  @JsonKey(name: "likes")
  final String? likes;
  @override
  @JsonKey(name: "portfolio")
  final String? portfolio;

  @override
  String toString() {
    return 'UserLinks(self: $self, html: $html, photos: $photos, likes: $likes, portfolio: $portfolio)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UserLinksImpl &&
            (identical(other.self, self) || other.self == self) &&
            (identical(other.html, html) || other.html == html) &&
            (identical(other.photos, photos) || other.photos == photos) &&
            (identical(other.likes, likes) || other.likes == likes) &&
            (identical(other.portfolio, portfolio) ||
                other.portfolio == portfolio));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, self, html, photos, likes, portfolio);

  /// Create a copy of UserLinks
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UserLinksImplCopyWith<_$UserLinksImpl> get copyWith =>
      __$$UserLinksImplCopyWithImpl<_$UserLinksImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$UserLinksImplToJson(
      this,
    );
  }
}

abstract class _UserLinks implements UserLinks {
  const factory _UserLinks(
      {@JsonKey(name: "self") final String? self,
      @JsonKey(name: "html") final String? html,
      @JsonKey(name: "photos") final String? photos,
      @JsonKey(name: "likes") final String? likes,
      @JsonKey(name: "portfolio") final String? portfolio}) = _$UserLinksImpl;

  factory _UserLinks.fromJson(Map<String, dynamic> json) =
      _$UserLinksImpl.fromJson;

  @override
  @JsonKey(name: "self")
  String? get self;
  @override
  @JsonKey(name: "html")
  String? get html;
  @override
  @JsonKey(name: "photos")
  String? get photos;
  @override
  @JsonKey(name: "likes")
  String? get likes;
  @override
  @JsonKey(name: "portfolio")
  String? get portfolio;

  /// Create a copy of UserLinks
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UserLinksImplCopyWith<_$UserLinksImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

ProfileImage _$ProfileImageFromJson(Map<String, dynamic> json) {
  return _ProfileImage.fromJson(json);
}

/// @nodoc
mixin _$ProfileImage {
  @JsonKey(name: "small")
  String? get small => throw _privateConstructorUsedError;
  @JsonKey(name: "medium")
  String? get medium => throw _privateConstructorUsedError;
  @JsonKey(name: "large")
  String? get large => throw _privateConstructorUsedError;

  /// Serializes this ProfileImage to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ProfileImage
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ProfileImageCopyWith<ProfileImage> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ProfileImageCopyWith<$Res> {
  factory $ProfileImageCopyWith(
          ProfileImage value, $Res Function(ProfileImage) then) =
      _$ProfileImageCopyWithImpl<$Res, ProfileImage>;
  @useResult
  $Res call(
      {@JsonKey(name: "small") String? small,
      @JsonKey(name: "medium") String? medium,
      @JsonKey(name: "large") String? large});
}

/// @nodoc
class _$ProfileImageCopyWithImpl<$Res, $Val extends ProfileImage>
    implements $ProfileImageCopyWith<$Res> {
  _$ProfileImageCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ProfileImage
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? small = freezed,
    Object? medium = freezed,
    Object? large = freezed,
  }) {
    return _then(_value.copyWith(
      small: freezed == small
          ? _value.small
          : small // ignore: cast_nullable_to_non_nullable
              as String?,
      medium: freezed == medium
          ? _value.medium
          : medium // ignore: cast_nullable_to_non_nullable
              as String?,
      large: freezed == large
          ? _value.large
          : large // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ProfileImageImplCopyWith<$Res>
    implements $ProfileImageCopyWith<$Res> {
  factory _$$ProfileImageImplCopyWith(
          _$ProfileImageImpl value, $Res Function(_$ProfileImageImpl) then) =
      __$$ProfileImageImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: "small") String? small,
      @JsonKey(name: "medium") String? medium,
      @JsonKey(name: "large") String? large});
}

/// @nodoc
class __$$ProfileImageImplCopyWithImpl<$Res>
    extends _$ProfileImageCopyWithImpl<$Res, _$ProfileImageImpl>
    implements _$$ProfileImageImplCopyWith<$Res> {
  __$$ProfileImageImplCopyWithImpl(
      _$ProfileImageImpl _value, $Res Function(_$ProfileImageImpl) _then)
      : super(_value, _then);

  /// Create a copy of ProfileImage
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? small = freezed,
    Object? medium = freezed,
    Object? large = freezed,
  }) {
    return _then(_$ProfileImageImpl(
      small: freezed == small
          ? _value.small
          : small // ignore: cast_nullable_to_non_nullable
              as String?,
      medium: freezed == medium
          ? _value.medium
          : medium // ignore: cast_nullable_to_non_nullable
              as String?,
      large: freezed == large
          ? _value.large
          : large // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ProfileImageImpl implements _ProfileImage {
  const _$ProfileImageImpl(
      {@JsonKey(name: "small") this.small,
      @JsonKey(name: "medium") this.medium,
      @JsonKey(name: "large") this.large});

  factory _$ProfileImageImpl.fromJson(Map<String, dynamic> json) =>
      _$$ProfileImageImplFromJson(json);

  @override
  @JsonKey(name: "small")
  final String? small;
  @override
  @JsonKey(name: "medium")
  final String? medium;
  @override
  @JsonKey(name: "large")
  final String? large;

  @override
  String toString() {
    return 'ProfileImage(small: $small, medium: $medium, large: $large)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ProfileImageImpl &&
            (identical(other.small, small) || other.small == small) &&
            (identical(other.medium, medium) || other.medium == medium) &&
            (identical(other.large, large) || other.large == large));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, small, medium, large);

  /// Create a copy of ProfileImage
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ProfileImageImplCopyWith<_$ProfileImageImpl> get copyWith =>
      __$$ProfileImageImplCopyWithImpl<_$ProfileImageImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ProfileImageImplToJson(
      this,
    );
  }
}

abstract class _ProfileImage implements ProfileImage {
  const factory _ProfileImage(
      {@JsonKey(name: "small") final String? small,
      @JsonKey(name: "medium") final String? medium,
      @JsonKey(name: "large") final String? large}) = _$ProfileImageImpl;

  factory _ProfileImage.fromJson(Map<String, dynamic> json) =
      _$ProfileImageImpl.fromJson;

  @override
  @JsonKey(name: "small")
  String? get small;
  @override
  @JsonKey(name: "medium")
  String? get medium;
  @override
  @JsonKey(name: "large")
  String? get large;

  /// Create a copy of ProfileImage
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ProfileImageImplCopyWith<_$ProfileImageImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
