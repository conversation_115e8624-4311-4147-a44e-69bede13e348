import 'package:pixs/features/home/<USER>/models/collection/collection_response.dart';
import 'package:pixs/features/home/<USER>/models/collection_photo/collection_photo_list_response.dart';
import 'package:pixs/features/home/<USER>/models/image/image_response.dart';
import 'package:pixs/shared/utils/result.dart';

import '../../../../shared/api/network/models/open_ai_response.dart';

abstract class HomeRepository {
  const HomeRepository();

  Future<ResponseResult<List<ImageResponse>>> getImages({required int page});
  Future<ResponseResult<List<ImageResponse>>> getUserImages(
      {required String username, required int page});
  Future<ResponseResult<List<CollectionResponse>>> getCollections(
      {required int page});
  Future<ResponseResult<List<CollectionPhotoListResponse>>> getCollectionPhotos(
      {required int page, required String collectionId});
  Future<ResponseResult<AiResponse>> getImagesByPrompt(
      {required String prompt, required int page});
}
