import 'dart:ui';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:fluentui_system_icons/fluentui_system_icons.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_blurhash/flutter_blurhash.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:pixs/features/home/<USER>/models/image/image_response.dart';
import 'package:pixs/features/home/<USER>/home/<USER>';
import 'package:pixs/shared/app/enums/api_fetch_status.dart';
import 'package:pixs/shared/app/extension/helper.dart';
import 'package:pixs/shared/constants/images.dart';
import 'package:pixs/shared/routes/routes.dart';
import 'package:animate_do/animate_do.dart';

class AuthorDetailScreen extends StatefulWidget {
  const AuthorDetailScreen({super.key, required this.image});
  final ImageResponse image;

  @override
  State<AuthorDetailScreen> createState() => _AuthorDetailScreenState();
}

class _AuthorDetailScreenState extends State<AuthorDetailScreen> {
  @override
  void initState() {
    super.initState();
    Helper.afterInit(_init);
  }

  void _init() {
    context
        .read<HomeCubit>()
        .getUserImages(username: widget.image.user?.username ?? '');
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: Container(
        decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.7),
            image: DecorationImage(
              fit: BoxFit.fill,
              image: NetworkImage(
                widget.image.urls?.small ?? '',
              ),
            )),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 10.0, sigmaY: 10.0),
          child: Container(
            decoration: BoxDecoration(color: Colors.white.withOpacity(0.0)),
            child: ListView(
              shrinkWrap: true,
              physics: const ScrollPhysics(),
              children: <Widget>[
                FadeInDown(
                  duration: const Duration(milliseconds: 500),
                  child: Padding(
                    padding: const EdgeInsets.only(top: 20),
                    child: Align(
                        alignment: Alignment.topLeft,
                        child: ClipOval(
                          child: Material(
                            color: Colors.transparent,
                            child: InkWell(
                              onTap: () => Navigator.pop(context),
                              child: const SizedBox(
                                  width: 56,
                                  height: 56,
                                  child: Icon(
                                    Icons.arrow_back_ios_sharp,
                                    color: Colors.white,
                                    semanticLabel: "Back",
                                    size: 20,
                                  )),
                            ),
                          ),
                        )),
                  ),
                ),
                FadeInUp(
                  duration: const Duration(milliseconds: 800),
                  child: Padding(
                    padding: const EdgeInsets.only(top: 90, left: 30, right: 30),
                    child: Align(
                      alignment: Alignment.topCenter,
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          CircleAvatar(
                            radius: 30.0,
                            backgroundColor: Colors.white,
                            child: CircleAvatar(
                              radius: 29.0,
                              backgroundImage: NetworkImage(
                                  widget.image.user?.profileImage?.medium ?? ''),
                              backgroundColor: Colors.transparent,
                            ),
                          ),
                          const SizedBox(height: 30),
                          Text(
                            '${widget.image.user?.name}',
                            maxLines: 3,
                            style: const TextStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.w700,
                                fontSize: 20),
                          ),
                          const SizedBox(height: 5),
                          const Text(
                            "on unsplash",
                            textAlign: TextAlign.center,
                            style: TextStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.w300,
                                fontSize: 14),
                          ),
                          const SizedBox(height: 15),
                          if (widget.image.user?.bio != null)
                            Text(
                              '${widget.image.user?.bio}',
                              textAlign: TextAlign.center,
                              style: const TextStyle(
                                  color: Colors.white,
                                  fontWeight: FontWeight.w300,
                                  fontSize: 14),
                            ),
                          const SizedBox(height: 10),
                          InkWell(
                            onTap: () {
                              if (widget.image.user?.links?.html != null) {
                                final url = '${widget.image.user!.links!.html}?utm_source=pixs&utm_medium=referral';
                                Helper().launchUrls(url);
                              }
                            },
                            child: const Icon(FluentIcons.web_asset_20_regular,
                                color: Colors.white, size: 20),
                          )
                        ],
                      ),
                    ),
                  ),
                ),
                Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(24),
                    ),
                    child: Column(
                      children: <Widget>[
                        const SizedBox(height: 12),
                        Container(
                          height: 5,
                          width: 30,
                          decoration: BoxDecoration(
                              color: Colors.grey[200],
                              borderRadius: BorderRadius.circular(16)),
                        ),
                        const SizedBox(height: 16),
                        Container(
                          decoration: BoxDecoration(
                              color: Colors.black,
                              borderRadius: BorderRadius.only(
                                  topLeft: Radius.circular(15.r),
                                  topRight: Radius.circular(15.r))),
                          child: BlocSelector<HomeCubit, HomeState,
                              (ApiFetchStatus, List<ImageResponse>?)>(
                            selector: (state) => (
                              state.userImageListFetchStatus,
                              state.userImages
                            ),
                            builder: (context, state) {
                              return GridView.builder(
                                shrinkWrap: true,
                                padding: EdgeInsets.only(
                                    top: 10.h, left: 5.w, right: 5.w),
                                physics: const NeverScrollableScrollPhysics(),
                                gridDelegate:
                                    const SliverGridDelegateWithFixedCrossAxisCount(
                                  crossAxisCount: 2,
                                  mainAxisSpacing: 8.0,
                                  crossAxisSpacing: 8.0,
                                ),
                                itemCount: state.$2?.length ?? 0,
                                itemBuilder: (context, index) {
                                  return FadeInUp(
                                    delay: Duration(milliseconds: 100 * index),
                                    duration: const Duration(milliseconds: 500),
                                    child: GestureDetector(
                                      onTap: () => Navigator.pushNamed(
                                        context,
                                        routeImageDetail,
                                        arguments: {'image': state.$2?[index]},
                                      ),
                                      child: CachedNetworkImage(
                                        cacheKey: state.$2?[index].urls?.small,
                                        fit: BoxFit.cover,
                                        imageUrl:
                                            state.$2?[index].urls?.small ?? '',
                                        progressIndicatorBuilder:
                                            (context, url, progress) => Opacity(
                                          opacity: 0.5,
                                          child: Center(
                                            child: BlurHash(
                                              hash:
                                                  state.$2?[index].blurHash ?? '',
                                            ),
                                          ),
                                        ),
                                        errorWidget: (context, url, error) =>
                                            Center(
                                          child: Opacity(
                                            opacity: 0.5,
                                            child: SizedBox(
                                              width: 50,
                                              child: Center(
                                                child: Image.asset(
                                                  Assets.kLogo,
                                                  width: 50,
                                                ),
                                              ),
                                            ),
                                          ),
                                        ),
                                      ),
                                    ),
                                  );
                                },
                              );
                            },
                          ),
                        ),
                      ],
                    ))
              ],
            ),
          ),
        ),
      ),
    );
  }
}