import 'dart:io';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:fluentui_system_icons/fluentui_system_icons.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:image_editor_plus/image_editor_plus.dart';
import 'package:image_editor_plus/options.dart';
import 'package:pixs/features/home/<USER>/models/image/image_response.dart';
import 'package:pixs/features/home/<USER>/home/<USER>';
import 'package:pixs/features/image_details/widgets/animated_favorite_button.dart';
import 'package:pixs/features/image_details/widgets/edited_image_options_dialog.dart';
import 'package:pixs/features/image_details/widgets/wallpaper_apply_dilaog.dart';
import 'package:pixs/shared/app/extension/helper.dart';
import 'package:pixs/shared/constants/colors.dart';
import 'package:pixs/shared/routes/routes.dart';
import 'package:pixs/shared/themes/font_palette.dart';
import 'package:pixs/shared/widgets/attribution_widget.dart';
import 'package:animate_do/animate_do.dart';

class ImageDetailsScreen extends StatefulWidget {
  const ImageDetailsScreen({super.key, required this.image});
  final ImageResponse image;

  @override
  State<ImageDetailsScreen> createState() => _ImageDetailsScreenState();
}

class _ImageDetailsScreenState extends State<ImageDetailsScreen> with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );
    _fadeAnimation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );

    // Start with buttons visible
    _animationController.value = 1.0;
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _toggleActionButtonVisibility(bool visible) {
    if (visible) {
      _animationController.forward();
    } else {
      _animationController.reverse();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      floatingActionButtonLocation: FloatingActionButtonLocation.miniEndFloat,
      floatingActionButton: FadeTransition(
        opacity: _fadeAnimation,
        child: SlideTransition(
          position: Tween<Offset>(
            begin: const Offset(0.5, 0),
            end: Offset.zero,
          ).animate(_fadeAnimation),
          child: _ActionButton(image: widget.image),
        ),
      ),
      body: GestureDetector(
        onTapDown: (details) => _toggleActionButtonVisibility(false),
        onTapUp: (details) => _toggleActionButtonVisibility(true),
        onTapCancel: () => _toggleActionButtonVisibility(true),
        child: Stack(
          children: [
            // Hero animation for smooth transition from grid
            Hero(
              tag: 'image_${widget.image.id}',
              child: CachedNetworkImage(
                imageUrl: widget.image.urls?.full ?? '',
                fit: BoxFit.cover,
                width: MediaQuery.of(context).size.width,
                height: MediaQuery.of(context).size.height,
                cacheKey: widget.image.urls?.full,
                maxWidthDiskCache: 1024,
                maxHeightDiskCache: 1024,
                progressIndicatorBuilder: (context, url, progress) => Stack(
                  children: [
                    // Blurred small image as background
                    Image.network(
                      widget.image.urls?.small ?? '',
                      fit: BoxFit.cover,
                      width: MediaQuery.of(context).size.width,
                      height: MediaQuery.of(context).size.height,
                    ),
                    // Loading indicator
                    if (progress.progress != 1.0)
                      Center(
                        child: CircularProgressIndicator(
                          value: progress.progress,
                          color: kSecondaryColor,
                        ),
                      ),
                  ],
                ),
              ),
            ),
            // Back button with animation
            Positioned(
              top: 55.h,
              left: 10.w,
              child: FadeTransition(
                opacity: _fadeAnimation,
                child: SlideTransition(
                  position: Tween<Offset>(
                    begin: const Offset(-0.5, 0),
                    end: Offset.zero,
                  ).animate(_fadeAnimation),
                  child: Container(
                    decoration: BoxDecoration(
                      color: Colors.black.withOpacity(0.3),
                      shape: BoxShape.circle,
                    ),
                    child: IconButton(
                      iconSize: 20.0,
                      icon: const Icon(
                        Icons.arrow_back_ios,
                        color: Colors.white,
                      ),
                      onPressed: () => Navigator.pop(context),
                    ),
                  ),
                ),
              ),
            ),
            // Attribution at the bottom
            Positioned(
              bottom: 20.h,
              left: 20.w,
              right: 20.w,
              child: FadeInUp(
                duration: const Duration(milliseconds: 600),
                delay: const Duration(milliseconds: 800),
                child: DetailAttributionWidget(image: widget.image),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _ActionButton extends StatelessWidget {
  const _ActionButton({required this.image});
  final ImageResponse image;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 0.w),
      child: Container(
        height: 300.h,
        width: 80.w,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(20.r),
          color: kBackgroundColor.withOpacity(0.8),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.2),
              blurRadius: 10,
              spreadRadius: 1,
            ),
          ],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: [
            10.verticalSpace,
            // Author avatar with animation
            ZoomIn(
              duration: const Duration(milliseconds: 400),
              delay: const Duration(milliseconds: 100),
              child: GestureDetector(
                onTap: () => Navigator.pushNamed(
                  context,
                  routeAuthorDetail,
                  arguments: {'image': image},
                ),
                child: Column(
                  children: [
                    CircleAvatar(
                      radius: 20.r,
                      backgroundColor: kPrimaryColor,
                      backgroundImage: image.user?.profileImage?.medium != null &&
                              image.user?.profileImage?.medium?.isNotEmpty == true

                          ? NetworkImage(image.user?.profileImage?.medium ?? '')
                          : const AssetImage('assets/icons/logo.png'),

                    ),
                    5.verticalSpace,
                    Text(
                      '${image.user?.name}',
                      textAlign: TextAlign.center,
                      style: FontPalette.urbenist10.copyWith(
                        color: kWhite,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            // Download button with animation
            FadeInRight(
              duration: const Duration(milliseconds: 400),
              delay: const Duration(milliseconds: 200),
              child: IconButton(
                onPressed: () async {
                  // Show a quick pulse animation when pressed
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: const Row(
                        children: [
                          Icon(Icons.check_circle, color: Colors.green),
                          SizedBox(width: 10),
                          Text('Image downloaded successfully'),
                        ],
                      ),
                      backgroundColor: Colors.black.withOpacity(0.7),
                      behavior: SnackBarBehavior.floating,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(10.r),
                      ),
                    ),
                  );
                  await Helper().downloadUnsplashImage(image: image);
                },
                icon: const Icon(FluentIcons.arrow_download_20_regular),
              ),
            ),
            // Favorite button with animation
            FadeInRight(
              duration: const Duration(milliseconds: 400),
              delay: const Duration(milliseconds: 300),
              child: BlocSelector<HomeCubit, HomeState, bool>(
                selector: (state) {
                  return state.wishListImages?.contains(image) ?? false;
                },
                builder: (context, isWishList) {
                  return AnimatedFavoriteButton(
                    image: image,
                    isWishList: isWishList,
                    onToggle: (isFavorite) {
                      if (isFavorite) {
                        context.read<HomeCubit>().addToWishList(image);
                      } else {
                        context.read<HomeCubit>().removeFromWishList(image);
                      }
                    },
                  );
                },
              ),
            ),
            // Edit button with animation
            FadeInRight(
              duration: const Duration(milliseconds: 400),
              delay: const Duration(milliseconds: 400),
              child: BlocSelector<HomeCubit, HomeState, bool>(
                selector: (state) => state.isWallpaperSetting,
                builder: (context, isWallpaperSetting) {
                  if (isWallpaperSetting) {
                    return CircleAvatar(
                      radius: 20.r,
                      backgroundColor: kSecondaryColor,
                      child: SizedBox(
                        height: 16.h,
                        width: 16.w,
                        child: const CircularProgressIndicator(
                          strokeWidth: 2.0,
                          color: kWhite,
                        ),
                      ),
                    );
                  }
                  return IconButton(
                    onPressed: () async {
                      Uint8List bytes =
                          (await NetworkAssetBundle(Uri.parse(image.urls?.full ?? '')).load(image.urls?.full ?? ''))
                              .buffer
                              .asUint8List();
                      if (context.mounted) {
                        final editedImage = await Navigator.push<Uint8List>(
                          context,
                          MaterialPageRoute(
                            builder: (context) => ImageEditor(
                              image: bytes,
                              imagePickerOption: const ImagePickerOption(
                                captureFromCamera: true,
                                pickFromGallery: true,
                              ),
                            ),
                          ),
                        );

                        // If we got an edited image back
                        if (editedImage != null && context.mounted) {
                          // Show options dialog
                          showModalBottomSheet(
                            context: context,
                            builder: (context) => EditedImageOptionsDialog(
                              imageBytes: editedImage,
                              authorName: image.user?.name ?? 'unknown',
                            ),
                          );
                        }
                      }
                    },
                    icon: const Icon(FluentIcons.filter_20_regular),
                  );
                },
              ),
            ),
            // Wallpaper button with animation
            if (Platform.isAndroid)
            FadeInRight(
              duration: const Duration(milliseconds: 400),
              delay: const Duration(milliseconds: 500),
              child: GestureDetector(
                onTap: () => showModalBottomSheet(
                  context: context,
                  builder: (context) => WallpaperApplyDialog(
                    imageUrl: image.urls?.full ?? '',
                    authorName: image.user?.name ?? '',
                  ),
                ),
                child: CircleAvatar(
                  radius: 20.r,
                  backgroundColor: kSecondaryColor,
                  child: const Icon(FluentIcons.image_20_regular),
                ),
              ),
            ),
            10.verticalSpace,
          ],
        ),
      ),
    );
  }
}
