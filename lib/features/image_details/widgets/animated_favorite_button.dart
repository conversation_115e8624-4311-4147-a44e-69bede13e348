import 'dart:math';

import 'package:fluentui_system_icons/fluentui_system_icons.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:pixs/features/home/<USER>/models/image/image_response.dart';
import 'package:pixs/features/home/<USER>/home/<USER>';
import 'package:pixs/shared/constants/colors.dart';
import 'package:simple_animations/simple_animations.dart';

// Particle model for heart explosion effect
class ParticleModel {
  final int id;
  final Color color;
  final double size;
  final double speed;
  final double angle;
  final Offset offset;

  ParticleModel({
    required this.id,
    required this.color,
    required this.size,
    required this.speed,
    required this.angle,
    required this.offset,
  });
}

class AnimatedFavoriteButton extends StatefulWidget {
  final ImageResponse image;
  final bool isWishList;
  final Function(bool) onToggle;

  const AnimatedFavoriteButton({
    super.key,
    required this.image,
    required this.isWishList,
    required this.onToggle,
  });

  @override
  State<AnimatedFavoriteButton> createState() => _AnimatedFavoriteButtonState();
}

class _AnimatedFavoriteButtonState extends State<AnimatedFavoriteButton> with TickerProviderStateMixin {
  late AnimationController _controller;
  late AnimationController _pulseController;
  late AnimationController _rotationController;
  late Animation<double> _scaleAnimation;
  late Animation<Color?> _colorAnimation;
  late Animation<double> _bounceAnimation;
  late Animation<double> _pulseAnimation;
  late Animation<double> _rotationAnimation;

  bool _isWishList = false;
  final List<ParticleModel> _particles = [];
  final Random _random = Random();

  @override
  void initState() {
    super.initState();
    _isWishList = widget.isWishList;

    // Main animation controller
    _controller = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    // Pulse animation controller (continuous when favorited)
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    // Rotation animation controller (for initial favorite)
    _rotationController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );

    // Scale animation with more dramatic effect
    _scaleAnimation = TweenSequence<double>([
      TweenSequenceItem(
        tween: Tween<double>(begin: 1.0, end: 1.8)
            .chain(CurveTween(curve: Curves.easeInOutBack)),
        weight: 30,
      ),
      TweenSequenceItem(
        tween: Tween<double>(begin: 1.8, end: 0.8)
            .chain(CurveTween(curve: Curves.easeInOutBack)),
        weight: 30,
      ),
      TweenSequenceItem(
        tween: Tween<double>(begin: 0.8, end: 1.2)
            .chain(CurveTween(curve: Curves.easeInOutBack)),
        weight: 20,
      ),
      TweenSequenceItem(
        tween: Tween<double>(begin: 1.2, end: 1.0)
            .chain(CurveTween(curve: Curves.easeInOutBack)),
        weight: 20,
      ),
    ]).animate(_controller);

    // Color animation with gradient effect
    _colorAnimation = ColorTween(
      begin: Colors.grey,
      end: kSecondaryColor,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: const Interval(0.0, 0.6, curve: Curves.easeIn),
    ));

    // Bounce animation with more elastic effect
    _bounceAnimation = TweenSequence<double>([
      TweenSequenceItem(
        tween: Tween<double>(begin: 0.0, end: 20.0)
            .chain(CurveTween(curve: Curves.easeOut)),
        weight: 40,
      ),
      TweenSequenceItem(
        tween: Tween<double>(begin: 20.0, end: -5.0)
            .chain(CurveTween(curve: Curves.easeInOut)),
        weight: 30,
      ),
      TweenSequenceItem(
        tween: Tween<double>(begin: -5.0, end: 0.0)
            .chain(CurveTween(curve: Curves.elasticOut)),
        weight: 30,
      ),
    ]).animate(CurvedAnimation(
      parent: _controller,
      curve: const Interval(0.2, 1.0),
    ));

    // Continuous pulse animation for favorited state
    _pulseAnimation = TweenSequence<double>([
      TweenSequenceItem(
        tween: Tween<double>(begin: 1.0, end: 1.15)
            .chain(CurveTween(curve: Curves.easeInOut)),
        weight: 50,
      ),
      TweenSequenceItem(
        tween: Tween<double>(begin: 1.15, end: 1.0)
            .chain(CurveTween(curve: Curves.easeInOut)),
        weight: 50,
      ),
    ]).animate(_pulseController);

    // Rotation animation for initial favorite
    _rotationAnimation = Tween<double>(
      begin: 0.0,
      end: 0.15,
    ).animate(CurvedAnimation(
      parent: _rotationController,
      curve: Curves.elasticInOut,
    ));

    // Set up initial state
    if (_isWishList) {
      _controller.value = 1.0;
      _pulseController.repeat();
    }

    // Generate particles
    _generateParticles();
  }

  void _generateParticles() {
    _particles.clear();
    for (int i = 0; i < 15; i++) {
      _particles.add(ParticleModel(
        id: i,
        color: _getRandomColor(),
        size: _random.nextDouble() * 6 + 2,
        speed: _random.nextDouble() * 2 + 1,
        angle: _random.nextDouble() * 2 * pi,
        offset: Offset(
          _random.nextDouble() * 20 - 10,
          _random.nextDouble() * 20 - 10,
        ),
      ));
    }
  }

  // Generate random colors for particles
  Color _getRandomColor() {
    final colors = [
      Colors.red.shade300,
      Colors.pink.shade300,
      Colors.purple.shade300,
      kSecondaryColor,
      Colors.pinkAccent,
      Colors.purpleAccent,
    ];
    return colors[_random.nextInt(colors.length)];
  }

  @override
  void didUpdateWidget(AnimatedFavoriteButton oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.isWishList != oldWidget.isWishList) {
      _isWishList = widget.isWishList;
      if (_isWishList) {
        _controller.forward();
        _rotationController.forward();
        _pulseController.repeat();
      } else {
        _controller.reverse();
        _rotationController.reverse();
        _pulseController.stop();
      }
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    _pulseController.dispose();
    _rotationController.dispose();
    super.dispose();
  }

  void _toggleFavorite() {
    setState(() {
      _isWishList = !_isWishList;
      _generateParticles(); // Regenerate particles for new effect
    });

    if (_isWishList) {
      _controller.reset();
      _controller.forward();
      _rotationController.reset();
      _rotationController.forward();
      _pulseController.repeat();
    } else {
      _controller.reverse();
      _rotationController.reverse();
      _pulseController.stop();
    }

    widget.onToggle(_isWishList);
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: Listenable.merge([_controller, _pulseController, _rotationController]),
      builder: (context, child) {
        // Apply continuous pulse animation when favorited
        final pulseScale = _isWishList ? _pulseAnimation.value : 1.0;

        return Transform.translate(
          offset: Offset(0, -_bounceAnimation.value),
          child: Transform.rotate(
            angle: _isWishList ? _rotationAnimation.value : 0.0,
            child: Transform.scale(
              scale: _scaleAnimation.value * pulseScale,
              child: Container(
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  boxShadow: _isWishList ? [
                    BoxShadow(
                      color: kSecondaryColor.withOpacity(0.3),
                      blurRadius: 8,
                      spreadRadius: 2,
                    ),
                  ] : [],
                ),
                child: IconButton(
                  onPressed: _toggleFavorite,
                  icon: Stack(
                    alignment: Alignment.center,
                    children: [
                      // Heart outline
                      Icon(
                        FluentIcons.heart_20_regular,
                        color: _isWishList ? Colors.transparent : Colors.grey,
                        size: 24.sp,
                      ),
                      // Filled heart
                      Icon(
                        FluentIcons.heart_20_filled,
                        color: _colorAnimation.value,
                        size: 24.sp,
                      ),
                      // Particles effect when favorited
                      if (_isWishList && _controller.status == AnimationStatus.forward)
                        ..._buildParticles(),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  List<Widget> _buildParticles() {
    final List<Widget> particles = [];

    // Add particles with different animations
    for (ParticleModel particle in _particles) {
      final distance = _controller.value * 30 * particle.speed;
      final x = cos(particle.angle) * distance + particle.offset.dx;
      final y = sin(particle.angle) * distance + particle.offset.dy;

      particles.add(
        Positioned(
          left: 12.sp + x,
          top: 12.sp + y,
          child: Transform.rotate(
            angle: particle.angle,
            child: Opacity(
              opacity: (1.0 - _controller.value).clamp(0.0, 1.0),
              child: Container(
                width: particle.size.sp,
                height: particle.size.sp,
                decoration: BoxDecoration(
                  color: particle.color,
                  shape: particle.id % 3 == 0 ? BoxShape.circle : BoxShape.rectangle,
                  borderRadius: particle.id % 3 == 0 ? null : BorderRadius.circular(2.r),
                ),
              ),
            ),
          ),
        ),
      );
    }

    return particles;
  }
}
