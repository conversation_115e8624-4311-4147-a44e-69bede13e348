import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:pixs/shared/constants/colors.dart';
import 'package:pixs/shared/themes/font_palette.dart';
import 'package:animate_do/animate_do.dart';

class DialogHeader extends StatelessWidget {
  final String title;
  const DialogHeader({
    super.key,
    required this.title,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        SizedBox(
          height: 60,
          width: MediaQuery.of(context).size.width,
          child: Padding(
            padding: EdgeInsets.only(left: 20.w, right: 5.w),
            child: Row(
              children: [
                // Title with animation
                Expanded(
                  child: FadeIn(
                    duration: const Duration(milliseconds: 400),
                    child: Row(
                      children: [
                        Text(
                          title,
                          style: FontPalette.urbenist18.copyWith(
                            fontWeight: FontWeight.w500,
                            color: kWhite.withAlpha(190),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                // Close button with animation
                ZoomIn(
                  duration: const Duration(milliseconds: 400),
                  child: Container(
                    decoration: BoxDecoration(
                      color: Colors.black.withOpacity(0.2),
                      shape: BoxShape.circle,
                    ),
                    child: IconButton(
                      onPressed: () {
                        Navigator.pop(context);
                      },
                      icon: const Icon(
                        Icons.close,
                        size: 20,
                        color: kWhite,

                      ),
                    ),
                  ),
                )
              ],
            ),
          ),
        ),
        // Animated divider
        FadeInLeft(
          duration: const Duration(milliseconds: 600),
          child: Container(
            height: 1,
            width: double.infinity,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.centerLeft,
                end: Alignment.centerRight,
                colors: [
                  Colors.purple.withOpacity(0.7),
                  Colors.blue.withOpacity(0.3),
                  Colors.purple.withOpacity(0.1),
                ],
              ),
            ),
          ),
        )
      ],
    );
  }
}
