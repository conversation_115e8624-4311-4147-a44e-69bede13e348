import 'dart:io';
import 'dart:typed_data';

import 'package:animate_do/animate_do.dart';
import 'package:async_wallpaper/async_wallpaper.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:pixs/features/home/<USER>/home/<USER>';
import 'package:pixs/features/image_details/widgets/dialog_helper.dart';
import 'package:pixs/shared/app/extension/helper.dart';
import 'package:pixs/shared/themes/font_palette.dart';

class EditedImageOptionsDialog extends StatefulWidget {
  final Uint8List imageBytes;
  final String authorName;

  const EditedImageOptionsDialog({
    Key? key,
    required this.imageBytes,
    required this.authorName,
  }) : super(key: key);

  @override
  State<EditedImageOptionsDialog> createState() => _EditedImageOptionsDialogState();
}

class _EditedImageOptionsDialogState extends State<EditedImageOptionsDialog> with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  int _selectedOption = -1;
  bool _isProcessing = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );
    _scaleAnimation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutBack,
    );
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _saveImage() async {
    setState(() {
      _isProcessing = true;
    });

    try {
      await Helper().saveEditedImage(
        imageBytes: widget.imageBytes,
        name: widget.authorName,
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Row(
              children: [
                Icon(Icons.check_circle, color: Colors.green),
                SizedBox(width: 10),
                Text('Image saved successfully'),
              ],
            ),
            backgroundColor: Colors.black.withOpacity(0.7),
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10.r),
            ),
          ),
        );
        Navigator.pop(context);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Row(
              children: [
                Icon(Icons.error, color: Colors.red),
                SizedBox(width: 10),
                Text('Failed to save image'),
              ],
            ),
            backgroundColor: Colors.black.withOpacity(0.7),
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10.r),
            ),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isProcessing = false;
        });
      }
    }
  }

  void _setWallpaper(int option) {
    // Store the values we need to avoid context issues
    final Uint8List imageBytes = widget.imageBytes;
    final HomeCubit homeCubit = context.read<HomeCubit>();
    
    setState(() {
      _selectedOption = option;
      _isProcessing = true;
    });

    // Close the dialog with a slight delay for animation
    Future.delayed(const Duration(milliseconds: 800), () {
      if (!mounted) return;
      Navigator.pop(context);
      
      // Set the wallpaper after dialog is closed
      homeCubit.setWallpaperFromFile(
        imageBytes: imageBytes,
        option: option,
      );
    });
  }

  @override
  Widget build(BuildContext context) {
    return ScaleTransition(
      scale: _scaleAnimation,
      child: SingleChildScrollView(
        child: Padding(
          padding: EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
          child: Container(
            decoration: BoxDecoration(
              color: Colors.black.withOpacity(0.9),
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(20.r),
                topRight: Radius.circular(20.r),
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.purple.withOpacity(0.2),
                  blurRadius: 10,
                  spreadRadius: 2,
                ),
              ],
            ),
            child: Stack(
              children: [
                // Background gradient effect
                Positioned.fill(
                  child: Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [
                          Colors.purple.withOpacity(0.1),
                          Colors.blue.withOpacity(0.05),
                        ],
                      ),
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(20.r),
                        topRight: Radius.circular(20.r),
                      ),
                    ),
                  ),
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    // Animated header
                    FadeIn(
                      duration: const Duration(milliseconds: 400),
                      child: const DialogHeader(title: 'Edit Image'),
                    ),
                    Padding(
                      padding: EdgeInsets.symmetric(horizontal: 20.w),
                      child: Column(
                        children: [
                          30.verticalSpace,
                          // Preview section
                          FadeInDown(
                            duration: const Duration(milliseconds: 500),
                            child: Container(
                              height: 80.h,
                              width: double.infinity,
                              margin: EdgeInsets.only(bottom: 20.h),
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(15.r),
                                border: Border.all(color: Colors.grey.withOpacity(0.3)),
                                gradient: LinearGradient(
                                  begin: Alignment.topLeft,
                                  end: Alignment.bottomRight,
                                  colors: [
                                    Colors.purple.withOpacity(0.2),
                                    Colors.blue.withOpacity(0.1),
                                  ],
                                ),
                              ),
                              child: Padding(
                                padding: const EdgeInsets.all(8.0),
                                
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                                  children: [
                                    // Image preview
                                    Container(
                                      height: 60.h,
                                      width: 60.w,
                                      decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(10.r),
                                        border: Border.all(color: Colors.white.withOpacity(0.5)),
                                        image: DecorationImage(
                                          image: MemoryImage(widget.imageBytes),
                                          fit: BoxFit.cover,
                                        ),
                                      ),
                                    ),
                                    // Info text
                                    Flexible(
                                      child: Padding(
                                        padding: EdgeInsets.symmetric(horizontal: 10.w),
                                        child: Text(
                                          'What would you like to do with your edited image?',
                                          style: TextStyle(
                                            color: Colors.white,
                                            fontSize: 12.sp,
                                          ),
                                          textAlign: TextAlign.center,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                          // Save Image button
                          FadeInLeft(
                            duration: const Duration(milliseconds: 600),
                            child: _buildActionButton(
                              title: 'Save to Gallery',
                              icon: Icons.save_alt,
                              onTap: _isProcessing ? null : _saveImage,
                              isSelected: _isProcessing && _selectedOption == -1,
                              isPrimary: true,
                            ),
                          ),
                          if (Platform.isAndroid)
                         ...[ 10.verticalSpace,
                          // Home Screen button
                          FadeInRight(
                            duration: const Duration(milliseconds: 700),
                            child: _buildActionButton(
                              title: 'Set as Home Screen',
                              icon: Icons.home_outlined,
                              onTap: _isProcessing ? null : () => _setWallpaper(AsyncWallpaper.HOME_SCREEN),
                              isSelected: _selectedOption == AsyncWallpaper.HOME_SCREEN,
                            ),
                          ),
                          10.verticalSpace,
                          // Lock Screen button
                          FadeInLeft(
                            duration: const Duration(milliseconds: 800),
                            child: _buildActionButton(
                              title: 'Set as Lock Screen',
                              icon: Icons.lock_outline,
                              onTap: _isProcessing ? null : () => _setWallpaper(AsyncWallpaper.LOCK_SCREEN),
                              isSelected: _selectedOption == AsyncWallpaper.LOCK_SCREEN,
                            ),
                          ),
                          10.verticalSpace,
                          // Both Screens button
                          FadeInUp(
                            duration: const Duration(milliseconds: 900),
                            child: _buildActionButton(
                              title: 'Set as Both Screens',
                              icon: Icons.smartphone,
                              onTap: _isProcessing ? null : () => _setWallpaper(AsyncWallpaper.BOTH_SCREENS),
                              isSelected: _selectedOption == AsyncWallpaper.BOTH_SCREENS,
                              isPrimary: true,
                            ),
                          ),],
                          30.verticalSpace,
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildActionButton({
    required String title,
    required IconData icon,
    required VoidCallback? onTap,
    bool isSelected = false,
    bool isPrimary = false,
  }) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      height: 50.h,
      width: double.infinity,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12.r),
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: isPrimary
              ? [
                  Colors.purple.withOpacity(isSelected ? 1.0 : 0.7),
                  Colors.blue.withOpacity(isSelected ? 1.0 : 0.5),
                ]
              : [
                  Colors.grey.withOpacity(isSelected ? 0.8 : 0.2),
                  Colors.blueGrey.withOpacity(isSelected ? 0.8 : 0.2),
                ],
        ),
        boxShadow: isSelected
            ? [
                BoxShadow(
                  color: isPrimary ? Colors.purple.withOpacity(0.5) : Colors.grey.withOpacity(0.3),
                  blurRadius: 8,
                  spreadRadius: 1,
                  offset: const Offset(0, 2),
                ),
              ]
            : [],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(12.r),
          onTap: onTap,
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.w),
            child: Row(
              children: [
                Icon(
                  icon,
                  color: Colors.white,
                  size: 20.sp,
                ),
                SizedBox(width: 12.w),
                Expanded(
                  child: Text(
                    title,
                    style: FontPalette.urbenist16.copyWith(
                      color: Colors.white,
                      fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                    ),
                  ),
                ),
                if (isSelected && _isProcessing)
                  SizedBox(
                    height: 20.h,
                    width: 20.w,
                    child: const CircularProgressIndicator(
                      strokeWidth: 2.0,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                else if (isSelected)
                  Icon(
                    Icons.check_circle,
                    color: Colors.white,
                    size: 20.sp,
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
