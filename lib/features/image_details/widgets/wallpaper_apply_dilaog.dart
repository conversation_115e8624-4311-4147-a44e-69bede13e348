import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:pixs/features/home/<USER>/home/<USER>';
import 'package:pixs/features/image_details/widgets/dialog_helper.dart';
import 'package:pixs/shared/themes/font_palette.dart';
import 'package:animate_do/animate_do.dart';

// Wallpaper location constants (since async_wallpaper is temporarily disabled)
class WallpaperLocation {
  static const int homeScreen = 1;
  static const int lockScreen = 2;
  static const int bothScreens = 3;
}

class WallpaperApplyDialog extends StatefulWidget {
  final String imageUrl;
  final String authorName;
  const WallpaperApplyDialog(
      {super.key, required this.imageUrl, required this.authorName});

  @override
  State<WallpaperApplyDialog> createState() => _WallpaperApplyDialogState();
}

class _WallpaperApplyDialogState extends State<WallpaperApplyDialog> with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  int _selectedOption = -1;
  bool _isProcessing = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );
    _scaleAnimation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutBack,
    );
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _setWallpaper(int option) {
    // Store the values we need to avoid context issues
    final String imageUrl = widget.imageUrl;
    final String authorName = widget.authorName;
    final HomeCubit homeCubit = context.read<HomeCubit>();

    setState(() {
      _selectedOption = option;
      _isProcessing = true;
    });

    // Close the dialog with a slight delay for animation
    Future.delayed(const Duration(milliseconds: 800), () {
      if (!mounted) return;
      Navigator.pop(context);

      // Set the wallpaper after dialog is closed
      homeCubit.setWallpaper(
        imageUrl: imageUrl,
        authorName: authorName,
        option: option,
      );
    });
  }

  @override
  Widget build(BuildContext context) {
    return ScaleTransition(
      scale: _scaleAnimation,
      child: SingleChildScrollView(
        child: Padding(
          padding: EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
          child: Container(
            decoration: BoxDecoration(
              color: Colors.black.withValues(alpha: 0.9),
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(20.r),
                topRight: Radius.circular(20.r),
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.purple.withValues(alpha: 0.2),
                  blurRadius: 10,
                  spreadRadius: 2,
                ),
              ],
            ),
            child: Stack(
              children: [
                // Background gradient effect
                Positioned.fill(
                  child: Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [
                          Colors.purple.withValues(alpha: 0.1),
                          Colors.blue.withValues(alpha: 0.05),
                        ],
                      ),
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(20.r),
                        topRight: Radius.circular(20.r),
                      ),
                    ),
                  ),
                ),
                SizedBox(
                  height: 300.h,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      // Animated header
                      FadeIn(
                        duration: const Duration(milliseconds: 400),
                        child: const DialogHeader(title: 'Set Wallpaper'),
                      ),
                      Padding(
                        padding: EdgeInsets.symmetric(horizontal: 20.w),
                        child: Column(
                          children: [
                            30.verticalSpace,
                            // Home Screen button
                            FadeInLeft(
                              duration: const Duration(milliseconds: 600),
                              child: _buildWallpaperButton(
                                title: 'Home Screen',
                                icon: Icons.home_outlined,
                                option: WallpaperLocation.homeScreen,
                                isSelected: _selectedOption == WallpaperLocation.homeScreen,
                              ),
                            ),
                            10.verticalSpace,
                            // Lock Screen button
                            FadeInRight(
                              duration: const Duration(milliseconds: 700),
                              child: _buildWallpaperButton(
                                title: 'Lock Screen',
                                icon: Icons.lock_outline,
                                option: WallpaperLocation.lockScreen,
                                isSelected: _selectedOption == WallpaperLocation.lockScreen,
                              ),
                            ),
                            10.verticalSpace,
                            // Both Screens button
                            FadeInUp(
                              duration: const Duration(milliseconds: 800),
                              child: _buildWallpaperButton(
                                title: 'Both Screens',
                                icon: Icons.smartphone,
                                option: WallpaperLocation.bothScreens,
                                isSelected: _selectedOption == WallpaperLocation.bothScreens,
                                isPrimary: true,
                              ),
                            ),
                            30.verticalSpace,
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildWallpaperButton({
    required String title,
    required IconData icon,
    required int option,
    bool isSelected = false,
    bool isPrimary = false,
  }) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      height: 50.h,
      width: double.infinity,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12.r),
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: isPrimary
              ? [
                  Colors.purple.withValues(alpha: isSelected ? 1.0 : 0.7),
                  Colors.blue.withValues(alpha: isSelected ? 1.0 : 0.5),
                ]
              : [
                  Colors.grey.withValues(alpha: isSelected ? 0.8 : 0.2),
                  Colors.blueGrey.withValues(alpha: isSelected ? 0.8 : 0.2),
                ],
        ),
        boxShadow: isSelected
            ? [
                BoxShadow(
                  color: isPrimary ? Colors.purple.withValues(alpha: 0.5) : Colors.grey.withValues(alpha: 0.3),
                  blurRadius: 8,
                  spreadRadius: 1,
                  offset: const Offset(0, 2),
                ),
              ]
            : [],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(12.r),
          onTap: _isProcessing ? null : () => _setWallpaper(option),
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.w),
            child: Row(
              children: [
                Icon(
                  icon,
                  color: Colors.white,
                  size: 20.sp,
                ),
                SizedBox(width: 12.w),
                Expanded(
                  child: Text(
                    title,
                    style: FontPalette.urbenist16.copyWith(
                      color: Colors.white,
                      fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                    ),
                  ),
                ),
                if (isSelected && _isProcessing)
                  SizedBox(
                    height: 20.h,
                    width: 20.w,
                    child: const CircularProgressIndicator(
                      strokeWidth: 2.0,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                else if (isSelected)
                  Icon(
                    Icons.check_circle,
                    color: Colors.white,
                    size: 20.sp,
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
