import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:pixs/features/home/<USER>/home/<USER>';
import 'package:pixs/shared/app/enums/api_fetch_status.dart';
import 'package:pixs/shared/routes/routes.dart';
import 'package:pixs/shared/widgets/animated_app_bar.dart';
import 'package:pixs/shared/widgets/animations.dart';

class AnimatedImageGenerationScreen extends StatefulWidget {
  final String prompt;

  const AnimatedImageGenerationScreen({
    super.key,
    required this.prompt,
  });

  @override
  State<AnimatedImageGenerationScreen> createState() => _AnimatedImageGenerationScreenState();
}

class _AnimatedImageGenerationScreenState extends State<AnimatedImageGenerationScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _progressController;
  late Animation<double> _progressAnimation;
  bool _isComplete = false;

  @override
  void initState() {
    super.initState();
    _progressController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 10),
    );
    _progressAnimation = Tween<double>(begin: 0.0, end: 0.95).animate(_progressController);

    _progressController.addListener(() {
      if (_progressController.isAnimating) {
        _updateProgress(_progressAnimation.value);
      }
    });

    _progressController.forward();

    // Start image generation
    _startImageGeneration();
  }

  @override
  void dispose() {
    _progressController.dispose();
    super.dispose();
  }

  void _updateProgress(double progress) {
    // This method is called when the progress animation updates
    // We don't need to do anything here since we're using the animation value directly
  }

  void _startImageGeneration() {
    // Set the prompt in the HomeCubit
    context.read<HomeCubit>().updatePrompt(widget.prompt);

    // Generate the image
    context.read<HomeCubit>().generateImage();
  }

  void _completeGeneration() {
    setState(() {
      _isComplete = true;
    });
  }

  void _navigateToImageDetails() {
    final generatedImage = context.read<HomeCubit>().getGeneratedImageResponse();
    if (generatedImage != null) {
      // Use pushReplacementNamed to replace the current screen instead of stacking a new one
      Navigator.pushReplacementNamed(
        context,
        routeImageDetail,
        arguments: {'image': generatedImage},
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AnimatedAppBar(
        title: 'Generating Image',
        backgroundColor: Colors.black,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.close),
            onPressed: () {
              Navigator.pop(context);
            },
          ),
        ],
      ),
      body: BlocListener<HomeCubit, HomeState>(
        listenWhen: (previous, current) =>
            previous.imageGenerationStatus != current.imageGenerationStatus &&
            current.imageGenerationStatus == ApiFetchStatus.success,
        listener: (context, state) {
          // Just complete the generation to show the success animation
          // The SuccessAnimation widget will handle navigation when it finishes
          _completeGeneration();
        },
        child: SafeArea(
          child: Padding(
            padding: EdgeInsets.all(20.w),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SlideInAnimation(
                  from: const Offset(0, -0.1),
                  child: FadeInAnimation(
                    child: Text(
                      'Creating your masterpiece',
                      style: TextStyle(
                        fontSize: 24.sp,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ),
                SizedBox(height: 12.h),
                SlideInAnimation(
                  delay: const Duration(milliseconds: 200),
                  from: const Offset(0, -0.1),
                  child: FadeInAnimation(
                    delay: const Duration(milliseconds: 200),
                    child: Text(
                      widget.prompt,
                      style: TextStyle(
                        fontSize: 16.sp,
                        color: Colors.grey[400],
                      ),
                    ),
                  ),
                ),
                SizedBox(height: 40.h),
                Expanded(
                  child: Center(
                    child: _isComplete
                        ? SuccessAnimation(
                            size: 200,
                            onFinish: _navigateToImageDetails,
                          )
                        : const GeneratingAnimation(
                            size: 200,
                            text: 'Please wait while we generate your image...',
                          ),
                  ),
                ),
                SizedBox(height: 40.h),
                SlideInAnimation(
                  from: const Offset(0, 0.1),
                  child: FadeInAnimation(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              'Generation Progress',
                              style: TextStyle(
                                fontSize: 16.sp,
                                fontWeight: FontWeight.w500,
                                color: Colors.white,
                              ),
                            ),
                            BlocBuilder<HomeCubit, HomeState>(
                              builder: (context, state) {
                                if (state.imageGenerationStatus == ApiFetchStatus.success) {
                                  return Text(
                                    '100%',
                                    style: TextStyle(
                                      fontSize: 16.sp,
                                      fontWeight: FontWeight.w500,
                                      color: Colors.green,
                                    ),
                                  );
                                }
                                return AnimatedBuilder(
                                  animation: _progressAnimation,
                                  builder: (context, child) {
                                    return Text(
                                      '${(_progressAnimation.value * 100).toInt()}%',
                                      style: TextStyle(
                                        fontSize: 16.sp,
                                        fontWeight: FontWeight.w500,
                                        color: Colors.white,
                                      ),
                                    );
                                  },
                                );
                              },
                            ),
                          ],
                        ),
                        SizedBox(height: 8.h),
                        BlocBuilder<HomeCubit, HomeState>(
                          builder: (context, state) {
                            if (state.imageGenerationStatus == ApiFetchStatus.success) {
                              return LinearProgressIndicator(
                                value: 1.0,
                                backgroundColor: Colors.grey[800],
                                valueColor: const AlwaysStoppedAnimation<Color>(Colors.green),
                                borderRadius: BorderRadius.circular(10.r),
                                minHeight: 10.h,
                              );
                            }
                            return AnimatedBuilder(
                              animation: _progressAnimation,
                              builder: (context, child) {
                                return LinearProgressIndicator(
                                  value: _progressAnimation.value,
                                  backgroundColor: Colors.grey[800],
                                  valueColor: const AlwaysStoppedAnimation<Color>(Colors.purple),
                                  borderRadius: BorderRadius.circular(10.r),
                                  minHeight: 10.h,
                                );
                              },
                            );
                          },
                        ),
                      ],
                    ),
                  ),
                ),
                SizedBox(height: 20.h),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
