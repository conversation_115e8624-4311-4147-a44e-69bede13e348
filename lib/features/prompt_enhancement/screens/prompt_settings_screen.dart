import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:pixs/shared/constants/colors.dart';
import 'package:pixs/shared/service/prompt_enhancement_service.dart';
import 'package:pixs/shared/widgets/animated_app_bar.dart';
import 'package:animate_do/animate_do.dart';

class PromptSettingsScreen extends StatefulWidget {
  final String originalPrompt;
  final Function(String) onPromptEnhanced;

  const PromptSettingsScreen({
    super.key,
    required this.originalPrompt,
    required this.onPromptEnhanced,
  });

  @override
  State<PromptSettingsScreen> createState() => _PromptSettingsScreenState();
}

class _PromptSettingsScreenState extends State<PromptSettingsScreen> {
  final PromptEnhancementService _enhancementService = PromptEnhancementService();
  
  bool _useTemplate = true;
  bool _addNegativePrompt = true;
  PromptStyle? _selectedStyle;
  String _enhancedPrompt = '';
  
  @override
  void initState() {
    super.initState();
    _updateEnhancedPrompt();
  }
  
  void _updateEnhancedPrompt() {
    setState(() {
      _enhancedPrompt = _enhancementService.enhancePrompt(
        widget.originalPrompt,
        useTemplate: _useTemplate,
        addNegativePrompt: _addNegativePrompt,
        style: _selectedStyle,
      );
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: kBackgroundColor,
      appBar: AnimatedAppBar(
        title: 'Enhance Your Prompt',
        backgroundColor: kBackgroundColor,
        foregroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            FadeInDown(
              duration: const Duration(milliseconds: 400),
              child: Container(
                padding: EdgeInsets.all(16.w),
                decoration: BoxDecoration(
                  color: Colors.black,
                  borderRadius: BorderRadius.circular(12.r),
                  border: Border.all(
                    color: Colors.purple.withOpacity(0.3),
                    width: 1,
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Your Original Prompt:',
                      style: TextStyle(
                        color: Colors.grey[400],
                        fontSize: 14.sp,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(height: 8.h),
                    Text(
                      widget.originalPrompt,
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 16.sp,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            SizedBox(height: 24.h),
            FadeInDown(
              duration: const Duration(milliseconds: 500),
              child: Text(
                'Enhancement Options',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 18.sp,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            SizedBox(height: 16.h),
            FadeInDown(
              duration: const Duration(milliseconds: 600),
              child: SwitchListTile(
                title: Text(
                  'Use Style Template',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 16.sp,
                  ),
                ),
                subtitle: Text(
                  'Apply a professional template to your prompt',
                  style: TextStyle(
                    color: Colors.grey[400],
                    fontSize: 12.sp,
                  ),
                ),
                value: _useTemplate,
                onChanged: (value) {
                  setState(() {
                    _useTemplate = value;
                  });
                  _updateEnhancedPrompt();
                },
                activeColor: Colors.purple,
                tileColor: Colors.black,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12.r),
                ),
              ),
            ),
            SizedBox(height: 12.h),
            FadeInDown(
              duration: const Duration(milliseconds: 700),
              child: SwitchListTile(
                title: Text(
                  'Add Negative Prompts',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 16.sp,
                  ),
                ),
                subtitle: Text(
                  'Add instructions to avoid common issues',
                  style: TextStyle(
                    color: Colors.grey[400],
                    fontSize: 12.sp,
                  ),
                ),
                value: _addNegativePrompt,
                onChanged: (value) {
                  setState(() {
                    _addNegativePrompt = value;
                  });
                  _updateEnhancedPrompt();
                },
                activeColor: Colors.purple,
                tileColor: Colors.black,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12.r),
                ),
              ),
            ),
            SizedBox(height: 24.h),
            if (_useTemplate) ...[
              FadeInDown(
                duration: const Duration(milliseconds: 800),
                child: Text(
                  'Select Style',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 18.sp,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              SizedBox(height: 16.h),
              FadeInDown(
                duration: const Duration(milliseconds: 900),
                child: Wrap(
                  spacing: 8.w,
                  runSpacing: 8.h,
                  children: [
                    _buildStyleChip(PromptStyle.photorealistic, 'Photorealistic'),
                    _buildStyleChip(PromptStyle.digitalArt, 'Digital Art'),
                    _buildStyleChip(PromptStyle.painting, 'Painting'),
                    _buildStyleChip(PromptStyle.anime, 'Anime'),
                    _buildStyleChip(PromptStyle.threeD, '3D Render'),
                    _buildStyleChip(PromptStyle.landscape, 'Landscape'),
                    _buildStyleChip(PromptStyle.portrait, 'Portrait'),
                    _buildStyleChip(null, 'Random Style'),
                  ],
                ),
              ),
            ],
            SizedBox(height: 24.h),
            FadeInDown(
              duration: const Duration(milliseconds: 1000),
              child: Container(
                padding: EdgeInsets.all(16.w),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [Colors.purple.withOpacity(0.2), Colors.blue.withOpacity(0.2)],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(12.r),
                  border: Border.all(
                    color: Colors.purple.withOpacity(0.5),
                    width: 1,
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'Enhanced Prompt:',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 16.sp,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        IconButton(
                          icon: const Icon(Icons.refresh, color: Colors.purple),
                          onPressed: _updateEnhancedPrompt,
                          tooltip: 'Generate a new variation',
                        ),
                      ],
                    ),
                    SizedBox(height: 8.h),
                    Text(
                      _enhancedPrompt,
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 14.sp,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            SizedBox(height: 32.h),
            FadeInDown(
              duration: const Duration(milliseconds: 1100),
              child: SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: () {
                    widget.onPromptEnhanced(_enhancedPrompt);
                    Navigator.pop(context);
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.purple,
                    foregroundColor: Colors.white,
                    padding: EdgeInsets.symmetric(vertical: 16.h),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12.r),
                    ),
                  ),
                  child: Text(
                    'Use Enhanced Prompt',
                    style: TextStyle(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildStyleChip(PromptStyle? style, String label) {
    final isSelected = _selectedStyle == style;
    
    return ChoiceChip(
      label: Text(
        label,
        style: TextStyle(
          color: isSelected ? Colors.white : Colors.grey[300],
          fontSize: 14.sp,
        ),
      ),
      selected: isSelected,
      onSelected: (selected) {
        setState(() {
          _selectedStyle = selected ? style : null;
        });
        _updateEnhancedPrompt();
      },
      backgroundColor: Colors.black,
      selectedColor: Colors.purple,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20.r),
        side: BorderSide(
          color: isSelected ? Colors.purple : Colors.grey.withOpacity(0.3),
          width: 1,
        ),
      ),
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
    );
  }
}
