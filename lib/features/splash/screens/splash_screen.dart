import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:pixs/shared/app/extension/helper.dart';
import 'package:pixs/shared/constants/colors.dart';
import 'package:pixs/shared/constants/images.dart';
import 'package:pixs/shared/routes/routes.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> {
  double _opacity = 0.0; // Add this variable for opacity
  @override
  void initState() {
    super.initState();
    Helper.afterInit(_initialFunction);
    _startAnimation();
  }

  void _initialFunction() {
    final currentContext = context;
    Future.delayed(const Duration(seconds: 3), () async {
      if (!currentContext.mounted) return;

      Navigator.pushNamedAndRemoveUntil(
          currentContext, routeMain, ModalRoute.withName(routeRoot));
    });
  }

  void _startAnimation() {
    Future.delayed(const Duration(seconds: 1), () {
      setState(() {
        _opacity = 1.0; // Change opacity to 1 after 1 second
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor:kBackgroundColor,
      body: Center(
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 30.w),
          child: AnimatedOpacity(
            opacity: _opacity, // Use the animated opacity variable
            duration: const Duration(seconds: 2),
            child: Hero(
              tag: 'logo',
              child: Image.asset(
               Assets.kLogo
              ),
            ),
          ),
        ),
      ),
    );
  }
}
