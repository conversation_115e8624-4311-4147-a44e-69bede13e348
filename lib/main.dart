import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:pixs/features/home/<USER>/home/<USER>';
import 'package:pixs/my_app.dart';
import 'package:pixs/shared/dependency_injection/injectable.dart';

import 'shared/service/open_ai_service.dart';

Future<void> main() async {
  configureDependencies();
  await Hive.initFlutter();
  await Hive.openBox('wishlist');
  await dotenv.load(fileName: ".env");
  AiGenerator.init(key: dotenv.env['OPEN_AI_GPT_KEY'] ?? '');
  runApp(MultiBlocProvider(
    providers: [
      BlocProvider(create: (context) => getIt<HomeCubit>()),
    ],
    child: const MyApp(),
  ));
}
