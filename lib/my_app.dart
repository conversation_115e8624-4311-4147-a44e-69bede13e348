import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:pixs/shared/routes/navigator.dart';
import 'package:pixs/shared/routes/route_generator.dart';
import 'package:pixs/shared/routes/routes.dart';
import 'package:pixs/shared/themes/app_theme.dart';

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
   Widget build(BuildContext context) {
    const designSize = Size(375, 812);

    return ScreenUtilInit(
      designSize: designSize,
      fontSizeResolver: (fontSize, instance) {
        final display = View.of(context).display;
        final screenSize = display.size / display.devicePixelRatio;
        final scaleWidth = screenSize.width / designSize.width;
        return fontSize * scaleWidth;
      },
      builder: (context, _) {
        return MaterialApp(
          debugShowCheckedModeBanner: false,
          title: 'Pixs',
          theme: AppTheme.lightTheme,
          themeMode: ThemeMode.light,
          onGenerateRoute: (settings) =>
              RouteGenerator.generateRoute(settings),
          initialRoute: routeRoot,
          navigatorKey: navigator<PERSON><PERSON>,
        );
      },
    );
  }
}
