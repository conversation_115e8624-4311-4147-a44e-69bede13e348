import 'dart:developer';
import 'dart:io';
import 'dart:typed_data';

import 'package:dio/dio.dart';
import 'package:flutter/cupertino.dart';
import 'package:get_it/get_it.dart';
import 'package:path_provider/path_provider.dart';
import 'package:pixs/features/home/<USER>/models/image/image_response.dart';
import 'package:pixs/shared/service/download_tracking_service.dart';
import 'package:pixs/shared/utils/failures/bad_request.dart';
import 'package:flutter_file_dialog/flutter_file_dialog.dart';
import 'package:http/http.dart' as http;
import 'package:url_launcher/url_launcher.dart';

class Helper {
  static void afterInit(Function function) {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      function();
    });
  }

  dynamic errorMapping(Response? response) {
    final badRequest = <BadRequest>[]; // List to store BadRequest objects
    var errorString = ''; // String to accumulate error messages

    // Iterate over response data keys
    (response?.data.keys.forEach((key) {
      if (key == 'message' ||
          key == 'error' ||
          key == 'detail' ||
          key == 'email') {
        final message = <String>[];
        if (key == 'email') {
          message.add(response.data[key][0]);
        } else {
          message.add(response.data[key]);
        }
        badRequest.add(
          BadRequest(
              errorField: '',
              error: message), // Add message/error to badRequest
        );
      } else {
        badRequest.add(
          BadRequest(
              errorField: key,
              error: List<String>.from(response.data[key]
                  .map((x) => x))), // Add other errors to badRequest
        );
      }
    }));

    // Construct error string from badRequest list
    for (var element in badRequest) {
      var subString = '';
      element.error?.forEach((sub) {
        subString = '$subString\n$sub';
      });
      if (errorString.isEmpty) {
        errorString =
            '${replaceCharacters(element.errorField ?? '')}$subString';
      } else {
        errorString =
            '$errorString\n\n${replaceCharacters(element.errorField ?? '')}$subString';
      }
    }

    // // Show error string in a snackbar
    // log('---------------------- errorString ----------------------');
    // log(errorString);
    // log('---------------------- errorString ----------------------');
    return errorString;
  }

  String replaceCharacters(String text) =>
      capitalizeFirstLetter(text.replaceAll(RegExp('[\\W_]+'), ' '));

  String capitalizeFirstLetter(String input) {
    if (input.isEmpty) {
      return input; // Return an empty string if the input is empty.
    }
    return input[0].toUpperCase() + input.substring(1);
  }

  Map<String, dynamic> removeNullValues(Map<String, dynamic> input) {
    return Map.fromEntries(input.entries.where((e) => e.value != null));
  }

  Future<void> downloadAndSaveImage({
    required String imageUrl,
    required String authorName,
    String? downloadLocation,
  }) async {
    try {
      // Track download with Unsplash API if downloadLocation is provided
      if (downloadLocation != null && downloadLocation.isNotEmpty) {
        final downloadTrackingService = GetIt.instance<DownloadTrackingService>();
        final trackingResult = await downloadTrackingService.trackDownload(downloadLocation);
        if (trackingResult.error != null) {
          log('Warning: Failed to track download: ${trackingResult.error}');
        }
      }

      final response = await http.get(Uri.parse(imageUrl));
      final directory = await getTemporaryDirectory();
      final filePath =
          '${directory.path}/unsplash_${authorName}_${DateTime.now().millisecondsSinceEpoch}.png';
      final file = File(filePath);
      await file.writeAsBytes(response.bodyBytes);

      final params = SaveFileDialogParams(sourceFilePath: file.path);
      final savedPath = await FlutterFileDialog.saveFile(params: params);

      if (savedPath != null) {
        log('Image saved to $savedPath');
      } else {
        log('Image not saved');
      }
    } catch (e) {
      log('Error downloading or saving image: $e');
    }
  }

  /// Download and save image with proper Unsplash API compliance
  Future<void> downloadUnsplashImage({
    required ImageResponse image,
  }) async {
    await downloadAndSaveImage(
      imageUrl: image.urls?.full ?? '',
      authorName: image.user?.name ?? 'unknown',
      downloadLocation: image.links?.downloadLocation,
    );
  }

  Future<void> saveEditedImage(
      {required Uint8List imageBytes, required String name}) async {
    try {
      final directory = await getTemporaryDirectory();
      final filePath =
          '${directory.path}/edited_${name}_${DateTime.now().millisecondsSinceEpoch}.png';
      final file = File(filePath);
      await file.writeAsBytes(imageBytes);

      final params = SaveFileDialogParams(sourceFilePath: file.path);
      final savedPath = await FlutterFileDialog.saveFile(params: params);

      if (savedPath != null) {
        log('Edited image saved to $savedPath');
      } else {
        log('Edited image not saved');
      }
    } catch (e) {
      log('Error saving edited image: $e');
    }
  }
    Future<void> launchUrls(String url) async {
    try {
      if (!await launchUrl(Uri.parse(url))) {
        throw Exception('Could not launch $url');
      }
    } catch (e) {
      log('Error launching URL: $e');
    }
  }

}
