import 'package:flutter/material.dart';
import 'package:pixs/features/category/screens/category_photo_screen.dart';
import 'package:pixs/features/home/<USER>/home_screen.dart';
import 'package:pixs/features/image_details/screens/auther_details.dart';
import 'package:pixs/features/image_details/screens/image_details_screen.dart';
import 'package:pixs/features/main/screens/main_screen.dart';
import 'package:pixs/features/splash/screens/splash_screen.dart';
import 'package:pixs/shared/routes/routes.dart';

class RouteGenerator {
  static Route<dynamic> generateRoute(RouteSettings settings) {
    final Object? args = settings.arguments;

    switch (settings.name) {
      case routeRoot:
        return MaterialPageRoute(builder: (_) => const SplashScreen());
      case routeMain:
        return MaterialPageRoute(builder: (_) => const MainScreen());
      case routeHome:
        return MaterialPageRoute(builder: (_) => const HomeScreen());
      case routeImageDetail:
        if (args is Map) {
          return PageRouteBuilder(
            pageBuilder: (context, animation, secondaryAnimation) =>
              ImageDetailsScreen(image: args['image']),
            transitionsBuilder: (context, animation, secondaryAnimation, child) {
              const begin = Offset(0.0, 1.0);
              const end = Offset.zero;
              const curve = Curves.easeOutCubic;

              var tween = Tween(begin: begin, end: end).chain(CurveTween(curve: curve));
              var offsetAnimation = animation.drive(tween);

              return SlideTransition(
                position: offsetAnimation,
                child: FadeTransition(
                  opacity: animation,
                  child: child,
                ),
              );
            },
            transitionDuration: const Duration(milliseconds: 500),
          );
        }
        return errorRoute(argsError: true);

      case routeAuthorDetail:
        if (args is Map) {
          return MaterialPageRoute(
              builder: (_) => AuthorDetailScreen(image: args['image']));
        }
        return errorRoute(argsError: true);
      case routeCategoryPhoto:
        print('=== RouteGenerator: routeCategoryPhoto called ===');
        print('Args type: ${args.runtimeType}');
        print('Args: $args');
        if (args is Map) {
          print('Args is Map, collection: ${args['collection']}');
          return MaterialPageRoute(
              builder: (_) {
                print('=== Building CategoryPhotoScreen ===');
                return CategoryPhotoScreen(collection: args['collection']);
              });
        }
        print('Args is NOT Map, returning error route');
        return errorRoute(argsError: true);
      default:
        return MaterialPageRoute(
          builder: (_) => Scaffold(
            body: Center(child: Text('No route defined for ${settings.name}')),
          ),
        );
    }
  }

  static Route<dynamic> errorRoute({String? error, bool argsError = false}) {
    return MaterialPageRoute(
      builder: (_) => Scaffold(
        appBar: AppBar(
          title: const Text('Error'),
          centerTitle: true,
        ),
        body: Center(
          child: Text(
            error ?? '${argsError ? 'Arguments' : 'Navigation'} Error',
            style: const TextStyle(
                fontWeight: FontWeight.w600, color: Colors.black54),
          ),
        ),
      ),
    );
  }
}
