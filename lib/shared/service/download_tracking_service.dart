import 'dart:developer';
import 'package:dio/dio.dart';
import 'package:injectable/injectable.dart';
import 'package:pixs/shared/api/network/network.dart';
import 'package:pixs/shared/utils/result.dart';

@LazySingleton()
class DownloadTrackingService {
  
  /// Tracks a download by calling the download_location endpoint
  /// This is required by Unsplash API guidelines before downloading any image
  Future<ResponseResult<bool>> trackDownload(String downloadLocation) async {
    try {
      // Remove the base URL if it's included in downloadLocation
      String endpoint = downloadLocation;
      if (downloadLocation.startsWith('https://api.unsplash.com')) {
        endpoint = downloadLocation.replaceFirst('https://api.unsplash.com', '');
      }
      
      final Response response = await NetworkProvider().get(endpoint);
      
      if (response.statusCode == 200 || response.statusCode == 201) {
        log('Download tracked successfully for: $downloadLocation');
        return ResponseResult(data: true);
      } else {
        log('Failed to track download: ${response.statusCode}');
        return ResponseResult(error: 'Failed to track download');
      }
    } on DioException catch (e) {
      log('Error tracking download: ${e.message}');
      return ResponseResult(error: e.message ?? 'Unknown error');
    } catch (e) {
      log('Unexpected error tracking download: $e');
      return ResponseResult(error: e.toString());
    }
  }
}
