import 'package:flutter/material.dart';
import 'package:pixs/shared/constants/colors.dart';
import 'package:pixs/shared/themes/font_palette.dart';

class AppTheme {
  static ThemeData lightTheme = ThemeData(
      colorScheme: ColorScheme.fromSeed(
        seedColor: kPrimaryColor,
      ),
      useMaterial3: true,
      visualDensity: VisualDensity.adaptivePlatformDensity,
      brightness: Brightness.light,
      scaffoldBackgroundColor: kBackgroundColor,
      appBarTheme: AppBarTheme(
        backgroundColor: kBackgroundColor,
        surfaceTintColor: Colors.transparent,
        titleTextStyle: FontPalette.urbenist18.copyWith(
          color: kWhite,
        ),
        iconTheme: const IconThemeData(
          color: kPrimaryColor,
        ),
      ),
      canvasColor: kSecondaryColor,
      // bottomNavigationBarTheme: BottomNavigationBarThemeData(
      //   backgroundColor: kPrimaryColor1,
      //   unselectedItemColor: kPrimaryColor.withOpacity(0.4),
      //   selectedItemColor: kPrimaryColor,
      // ),
      listTileTheme: ListTileThemeData(
        iconColor: kSecondaryColor,
        textColor: kWhite,
      ),
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: kWhite,
        labelStyle: TextStyle(
          color: kBlack.withOpacity(0.8),
        ),
        errorStyle: const TextStyle(color: kRedColor),
        errorMaxLines: 5,
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(
            style: BorderStyle.solid,
            color: kSecondaryColor,
          ),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(
            style: BorderStyle.solid,
            color: kRedColor,
          ),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(
            style: BorderStyle.solid,
            color: kPrimaryColor,
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(
            style: BorderStyle.solid,
            color: kPrimaryColor,
          ),
        ),
      ),
      textTheme: FontPalette.textLightTheme,
      primaryTextTheme: FontPalette.textLightTheme,
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ButtonStyle(
          backgroundColor: WidgetStateProperty.all<Color>(kPrimaryColor),
          foregroundColor: WidgetStateProperty.all<Color>(kWhite),
          shape: WidgetStateProperty.all<RoundedRectangleBorder>(
            RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(15),
              // side: BorderSide(color: Colors.red),
            ),
          ),
        ),
      ),
      fontFamily: FontPalette.themeFont,
      bottomNavigationBarTheme: const BottomNavigationBarThemeData(
        backgroundColor: kBackgroundColor,
      ),
      iconButtonTheme: IconButtonThemeData(
        style: ButtonStyle(
          iconColor: WidgetStateProperty.all<Color>(
              kWhite), // Updated to use MaterialStateProperty
        ),
      ));
}
