import 'package:flutter/material.dart';

/// A utility class for route transitions
class RouteTransitions {
  /// Navigate to a new page with a custom transition
  static Future<T?> slideIn<T>(
    BuildContext context,
    Widget page, {
    bool replace = false,
    Duration duration = const Duration(milliseconds: 300),
  }) {
    if (replace) {
      return Navigator.pushReplacement(
        context,
        PageRouteBuilder(
          transitionDuration: duration,
          pageBuilder: (context, animation, secondaryAnimation) => page,
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            const begin = Offset(1.0, 0.0);
            const end = Offset.zero;
            const curve = Curves.easeInOutCubic;

            var tween = Tween(begin: begin, end: end).chain(CurveTween(curve: curve));
            var offsetAnimation = animation.drive(tween);

            return SlideTransition(
              position: offsetAnimation,
              child: child,
            );
          },
        ),
      );
    } else {
      return Navigator.push(
        context,
        PageRouteBuilder(
          transitionDuration: duration,
          pageBuilder: (context, animation, secondaryAnimation) => page,
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            const begin = Offset(1.0, 0.0);
            const end = Offset.zero;
            const curve = Curves.easeInOutCubic;

            var tween = Tween(begin: begin, end: end).chain(CurveTween(curve: curve));
            var offsetAnimation = animation.drive(tween);

            return SlideTransition(
              position: offsetAnimation,
              child: child,
            );
          },
        ),
      );
    }
  }

  /// Navigate to a new page with a fade transition
  static Future<T?> fadeIn<T>(
    BuildContext context,
    Widget page, {
    bool replace = false,
    Duration duration = const Duration(milliseconds: 300),
  }) {
    if (replace) {
      return Navigator.pushReplacement(
        context,
        PageRouteBuilder(
          transitionDuration: duration,
          pageBuilder: (context, animation, secondaryAnimation) => page,
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            return FadeTransition(
              opacity: animation,
              child: child,
            );
          },
        ),
      );
    } else {
      return Navigator.push(
        context,
        PageRouteBuilder(
          transitionDuration: duration,
          pageBuilder: (context, animation, secondaryAnimation) => page,
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            return FadeTransition(
              opacity: animation,
              child: child,
            );
          },
        ),
      );
    }
  }

  /// Navigate to a new page with a scale transition
  static Future<T?> scale<T>(
    BuildContext context,
    Widget page, {
    bool replace = false,
    Duration duration = const Duration(milliseconds: 300),
  }) {
    if (replace) {
      return Navigator.pushReplacement(
        context,
        PageRouteBuilder(
          transitionDuration: duration,
          pageBuilder: (context, animation, secondaryAnimation) => page,
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            return ScaleTransition(
              scale: animation,
              child: child,
            );
          },
        ),
      );
    } else {
      return Navigator.push(
        context,
        PageRouteBuilder(
          transitionDuration: duration,
          pageBuilder: (context, animation, secondaryAnimation) => page,
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            return ScaleTransition(
              scale: animation,
              child: child,
            );
          },
        ),
      );
    }
  }

  /// Navigate to a new page with a combined transition (slide + fade)
  static Future<T?> slideAndFade<T>(
    BuildContext context,
    Widget page, {
    bool replace = false,
    Duration duration = const Duration(milliseconds: 300),
    Offset begin = const Offset(1.0, 0.0),
  }) {
    if (replace) {
      return Navigator.pushReplacement(
        context,
        PageRouteBuilder(
          transitionDuration: duration,
          pageBuilder: (context, animation, secondaryAnimation) => page,
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            const end = Offset.zero;
            const curve = Curves.easeInOutCubic;

            var tween = Tween(begin: begin, end: end).chain(CurveTween(curve: curve));
            var offsetAnimation = animation.drive(tween);

            return SlideTransition(
              position: offsetAnimation,
              child: FadeTransition(
                opacity: animation,
                child: child,
              ),
            );
          },
        ),
      );
    } else {
      return Navigator.push(
        context,
        PageRouteBuilder(
          transitionDuration: duration,
          pageBuilder: (context, animation, secondaryAnimation) => page,
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            const end = Offset.zero;
            const curve = Curves.easeInOutCubic;

            var tween = Tween(begin: begin, end: end).chain(CurveTween(curve: curve));
            var offsetAnimation = animation.drive(tween);

            return SlideTransition(
              position: offsetAnimation,
              child: FadeTransition(
                opacity: animation,
                child: child,
              ),
            );
          },
        ),
      );
    }
  }
}
