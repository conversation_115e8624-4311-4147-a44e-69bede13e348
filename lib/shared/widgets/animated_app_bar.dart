import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_animate/flutter_animate.dart';

class AnimatedAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String title;
  final List<Widget>? actions;
  final bool centerTitle;
  final Color? backgroundColor;
  final Color? foregroundColor;
  final bool showBackButton;
  final VoidCallback? onBackPressed;
  final Widget? leading;
  final double elevation;
  final Duration animationDuration;

  const AnimatedAppBar({
    Key? key,
    required this.title,
    this.actions,
    this.centerTitle = true,
    this.backgroundColor,
    this.foregroundColor,
    this.showBackButton = true,
    this.onBackPressed,
    this.leading,
    this.elevation = 0,
    this.animationDuration = const Duration(milliseconds: 500),
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return AppBar(
      title: Text(
        title,
        style: TextStyle(
          fontSize: 18.sp,
          fontWeight: FontWeight.bold,
          color: foregroundColor ?? Colors.white,
        ),
      )
          .animate()
          .fadeIn(duration: animationDuration)
          .slideY(begin: -0.2, end: 0, duration: animationDuration, curve: Curves.easeOutQuad),
      centerTitle: centerTitle,
      backgroundColor: backgroundColor ?? Theme.of(context).scaffoldBackgroundColor,
      foregroundColor: foregroundColor ?? Colors.white,
      elevation: elevation,
      leading: showBackButton
          ? (leading ??
              IconButton(
                icon: const Icon(Icons.arrow_back_ios_new_rounded),
                onPressed: onBackPressed ?? () => Navigator.of(context).pop(),
              ).animate().fadeIn(duration: animationDuration).slideX(
                  begin: -0.2, end: 0, duration: animationDuration, curve: Curves.easeOutQuad))
          : leading,
      actions: actions != null
          ? actions!
              .map((action) => action
                  .animate()
                  .fadeIn(duration: animationDuration)
                  .slideX(
                      begin: 0.2, end: 0, duration: animationDuration, curve: Curves.easeOutQuad))
              .toList()
          : null,
    );
  }

  @override
  Size get preferredSize => Size.fromHeight(kToolbarHeight);
}
