import 'package:flutter/material.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class AnimatedGridView extends StatelessWidget {
  final int itemCount;
  final IndexedWidgetBuilder itemBuilder;
  final int crossAxisCount;
  final double mainAxisSpacing;
  final double crossAxisSpacing;
  final EdgeInsetsGeometry padding;
  final ScrollPhysics? physics;
  final bool shrinkWrap;
  final ScrollController? controller;
  final Duration duration;
  final Duration delay;
  final bool animate;

  const AnimatedGridView({
    Key? key,
    required this.itemCount,
    required this.itemBuilder,
    this.crossAxisCount = 2,
    this.mainAxisSpacing = 16,
    this.crossAxisSpacing = 16,
    this.padding = const EdgeInsets.all(16),
    this.physics,
    this.shrinkWrap = false,
    this.controller,
    this.duration = const Duration(milliseconds: 500),
    this.delay = const Duration(milliseconds: 50),
    this.animate = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (!animate) {
      return GridView.builder(
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: crossAxisCount,
          mainAxisSpacing: mainAxisSpacing.h,
          crossAxisSpacing: crossAxisSpacing.w,
        ),
        itemCount: itemCount,
        itemBuilder: itemBuilder,
        padding: padding,
        physics: physics,
        shrinkWrap: shrinkWrap,
        controller: controller,
      );
    }

    return AnimationLimiter(
      child: GridView.builder(
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: crossAxisCount,
          mainAxisSpacing: mainAxisSpacing.h,
          crossAxisSpacing: crossAxisSpacing.w,
        ),
        itemCount: itemCount,
        padding: padding,
        physics: physics,
        shrinkWrap: shrinkWrap,
        controller: controller,
        itemBuilder: (context, index) {
          return AnimationConfiguration.staggeredGrid(
            position: index,
            duration: duration,
            columnCount: crossAxisCount,
            child: SlideAnimation(
              verticalOffset: 50.0,
              child: FadeInAnimation(
                delay: Duration(milliseconds: index * delay.inMilliseconds),
                child: ScaleAnimation(
                  scale: 0.9,
                  child: itemBuilder(context, index),
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}
