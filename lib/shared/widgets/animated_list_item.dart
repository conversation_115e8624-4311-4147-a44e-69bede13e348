import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class AnimatedListItem extends StatelessWidget {
  final Widget child;
  final int index;
  final int itemCount;
  final Duration baseDuration;
  final Duration staggerDuration;
  final Curve curve;
  final bool animate;
  final VoidCallback? onTap;
  final EdgeInsetsGeometry? padding;
  final Color? backgroundColor;
  final double borderRadius;
  final double? elevation;

  const AnimatedListItem({
    Key? key,
    required this.child,
    required this.index,
    required this.itemCount,
    this.baseDuration = const Duration(milliseconds: 400),
    this.staggerDuration = const Duration(milliseconds: 50),
    this.curve = Curves.easeOutQuad,
    this.animate = true,
    this.onTap,
    this.padding,
    this.backgroundColor,
    this.borderRadius = 12,
    this.elevation,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final delay = Duration(milliseconds: index * staggerDuration.inMilliseconds);
    
    Widget content = Container(
      padding: padding,
      decoration: BoxDecoration(
        color: backgroundColor ?? Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(borderRadius.r),
        boxShadow: elevation != null
            ? [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: elevation!,
                  offset: Offset(0, elevation! / 2),
                ),
              ]
            : null,
      ),
      child: child,
    );

    if (onTap != null) {
      content = InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(borderRadius.r),
        child: content,
      );
    }

    if (!animate) {
      return content;
    }

    return content
        .animate()
        .fadeIn(
          duration: baseDuration,
          delay: delay,
          curve: curve,
        )
        .slideY(
          begin: 0.2,
          end: 0,
          duration: baseDuration,
          delay: delay,
          curve: curve,
        )
        .scale(
          begin: const Offset(0.95, 0.95),
          end: const Offset(1, 1),
          duration: baseDuration,
          delay: delay,
          curve: curve,
        );
  }
}
