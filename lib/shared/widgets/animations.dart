import 'dart:math';

import 'package:animate_do/animate_do.dart';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:lottie/lottie.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// A loading animation widget using Lottie
class LoadingAnimation extends StatelessWidget {
  final double size;
  final bool repeat;

  const LoadingAnimation({
    Key? key,
    this.size = 200,
    this.repeat = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Lottie.asset(
        'assets/animations/loading.json',
        width: size.w,
        height: size.w,
        repeat: repeat,
        animate: true,
      ),
    );
  }
}

/// A success animation widget using Lottie
class SuccessAnimation extends StatelessWidget {
  final double size;
  final VoidCallback? onFinish;

  const SuccessAnimation({
    Key? key,
    this.size = 200,
    this.onFinish,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Lottie.asset(
        'assets/animations/success.json',
        width: size.w,
        height: size.w,
        repeat: false,
        animate: true,
        onLoaded: (composition) {
          if (onFinish != null) {
            Future.delayed(composition.duration, onFinish!);
          }
        },
      ),
    );
  }
}

/// A generating animation widget using Lottie
class GeneratingAnimation extends StatelessWidget {
  final double size;
  final String? text;

  const GeneratingAnimation({
    Key? key,
    this.size = 200,
    this.text,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Lottie.asset(
          'assets/animations/generating.json',
          width: size.w,
          height: size.w,
          repeat: true,
          animate: true,
        ),
        if (text != null) ...[
          SizedBox(height: 16.h),
          Text(
            text!,
            style: TextStyle(
              fontSize: 16.sp,
              fontWeight: FontWeight.w500,
              color: Colors.white,
            ),
          ).animate(onPlay: (controller) => controller.repeat())
            .fadeIn(duration: 600.ms)
            .then(delay: 200.ms)
            .fadeOut(duration: 600.ms),
        ],
      ],
    );
  }
}

/// A placeholder animation for images
class ImagePlaceholderAnimation extends StatelessWidget {
  final double width;
  final double height;

  const ImagePlaceholderAnimation({
    Key? key,
    required this.width,
    required this.height,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        color: Colors.grey[900],
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Center(
        child: Lottie.asset(
          'assets/animations/image_placeholder.json',
          width: width * 0.5,
          height: height * 0.5,
          repeat: true,
          animate: true,
        ),
      ),
    );
  }
}

/// A fade in animation wrapper
class FadeInAnimation extends StatelessWidget {
  final Widget child;
  final Duration duration;
  final Duration delay;
  final Curve curve;

  const FadeInAnimation({
    Key? key,
    required this.child,
    this.duration = const Duration(milliseconds: 500),
    this.delay = Duration.zero,
    this.curve = Curves.easeInOut,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return FadeIn(
      duration: duration,
      delay: delay,
      curve: curve,
      child: child,
    );
  }
}

/// A slide in animation wrapper
class SlideInAnimation extends StatelessWidget {
  final Widget child;
  final Duration duration;
  final Duration delay;
  final Offset from;
  final Curve curve;

  const SlideInAnimation({
    Key? key,
    required this.child,
    this.duration = const Duration(milliseconds: 500),
    this.delay = Duration.zero,
    this.from = const Offset(0, 0.1),
    this.curve = Curves.easeInOut,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SlideInUp(
      duration: duration,
      delay: delay,
      from: from.dy * 100,
      curve: curve,
      child: child,
    );
  }
}

/// A scale animation wrapper
class ScaleAnimation extends StatelessWidget {
  final Widget child;
  final Duration duration;
  final Duration delay;
  final Curve curve;

  const ScaleAnimation({
    Key? key,
    required this.child,
    this.duration = const Duration(milliseconds: 500),
    this.delay = Duration.zero,
    this.curve = Curves.easeInOut,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ZoomIn(
      duration: duration,
      delay: delay,
      curve: curve,
      child: child,
    );
  }
}

/// A pulse animation wrapper
class PulseAnimation extends StatelessWidget {
  final Widget child;
  final Duration duration;
  final bool infinite;

  const PulseAnimation({
    Key? key,
    required this.child,
    this.duration = const Duration(milliseconds: 1500),
    this.infinite = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Pulse(
      duration: duration,
      infinite: infinite,
      child: child,
    );
  }
}

/// A bounce animation wrapper
class BounceAnimation extends StatelessWidget {
  final Widget child;
  final Duration duration;
  final bool infinite;

  const BounceAnimation({
    Key? key,
    required this.child,
    this.duration = const Duration(milliseconds: 1500),
    this.infinite = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Bounce(
      duration: duration,
      infinite: infinite,
      child: child,
    );
  }
}

/// A custom animated button
class AnimatedButton extends StatefulWidget {
  final Widget child;
  final VoidCallback onTap;
  final Color? color;
  final double borderRadius;
  final EdgeInsetsGeometry padding;

  const AnimatedButton({
    Key? key,
    required this.child,
    required this.onTap,
    this.color,
    this.borderRadius = 12,
    this.padding = const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
  }) : super(key: key);

  @override
  State<AnimatedButton> createState() => _AnimatedButtonState();
}

class _AnimatedButtonState extends State<AnimatedButton> with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 150),
    );
    _scaleAnimation = Tween<double>(begin: 1.0, end: 0.95).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTapDown: (_) => _controller.forward(),
      onTapUp: (_) {
        _controller.reverse();
        widget.onTap();
      },
      onTapCancel: () => _controller.reverse(),
      child: AnimatedBuilder(
        animation: _scaleAnimation,
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: Container(
              padding: widget.padding,
              decoration: BoxDecoration(
                color: widget.color ?? Theme.of(context).primaryColor,
                borderRadius: BorderRadius.circular(widget.borderRadius.r),
                boxShadow: [
                  BoxShadow(
                    color: (widget.color ?? Theme.of(context).primaryColor).withOpacity(0.3),
                    blurRadius: 8,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: widget.child,
            ),
          );
        },
      ),
    );
  }
}

/// A custom page transition
class CustomPageRoute<T> extends PageRouteBuilder<T> {
  final Widget page;

  CustomPageRoute({required this.page})
      : super(
          pageBuilder: (context, animation, secondaryAnimation) => page,
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            const begin = Offset(1.0, 0.0);
            const end = Offset.zero;
            const curve = Curves.easeInOutCubic;

            var tween = Tween(begin: begin, end: end).chain(CurveTween(curve: curve));
            var offsetAnimation = animation.drive(tween);

            return SlideTransition(
              position: offsetAnimation,
              child: FadeTransition(
                opacity: animation,
                child: child,
              ),
            );
          },
        );
}

/// An animated auto_awesome icon with multiple effects
class AnimatedAutoAwesomeIcon extends StatefulWidget {
  final Color color;
  final double size;
  final bool isActive;
  final VoidCallback? onTap;

  const AnimatedAutoAwesomeIcon({
    super.key,
    this.color = Colors.purple,
    this.size = 20,
    this.isActive = true,
    this.onTap,
  });

  @override
  State<AnimatedAutoAwesomeIcon> createState() => _AnimatedAutoAwesomeIconState();
}

class _AnimatedAutoAwesomeIconState extends State<AnimatedAutoAwesomeIcon> with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _rotationAnimation;
  late Animation<double> _scaleAnimation;
  late Animation<double> _opacityAnimation;

  final List<Color> _particleColors = [
    Colors.purple,
    Colors.purpleAccent,
    Colors.deepPurple,
    Colors.blue,
    Colors.blueAccent,
  ];

  final Random _random = Random();

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 3000),
    );

    _rotationAnimation = TweenSequence<double>([
      TweenSequenceItem(
        tween: Tween<double>(begin: 0, end: 0.05)
            .chain(CurveTween(curve: Curves.easeInOut)),
        weight: 30,
      ),
      TweenSequenceItem(
        tween: Tween<double>(begin: 0.05, end: -0.05)
            .chain(CurveTween(curve: Curves.easeInOut)),
        weight: 40,
      ),
      TweenSequenceItem(
        tween: Tween<double>(begin: -0.05, end: 0)
            .chain(CurveTween(curve: Curves.easeInOut)),
        weight: 30,
      ),
    ]).animate(_controller);

    _scaleAnimation = TweenSequence<double>([
      TweenSequenceItem(
        tween: Tween<double>(begin: 1.0, end: 1.15)
            .chain(CurveTween(curve: Curves.easeInOut)),
        weight: 50,
      ),
      TweenSequenceItem(
        tween: Tween<double>(begin: 1.15, end: 1.0)
            .chain(CurveTween(curve: Curves.easeInOut)),
        weight: 50,
      ),
    ]).animate(_controller);

    _opacityAnimation = TweenSequence<double>([
      TweenSequenceItem(
        tween: Tween<double>(begin: 0.7, end: 1.0)
            .chain(CurveTween(curve: Curves.easeInOut)),
        weight: 50,
      ),
      TweenSequenceItem(
        tween: Tween<double>(begin: 1.0, end: 0.7)
            .chain(CurveTween(curve: Curves.easeInOut)),
        weight: 50,
      ),
    ]).animate(_controller);

    if (widget.isActive) {
      _controller.repeat();
    }
  }

  @override
  void didUpdateWidget(AnimatedAutoAwesomeIcon oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.isActive != oldWidget.isActive) {
      if (widget.isActive) {
        _controller.repeat();
      } else {
        _controller.stop();
      }
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: widget.onTap,
      child: AnimatedBuilder(
        animation: _controller,
        builder: (context, child) {
          return Transform.rotate(
            angle: _rotationAnimation.value * pi,
            child: Transform.scale(
              scale: _scaleAnimation.value,
              child: Stack(
                alignment: Alignment.center,
                children: [
                  // Particles effect
                  if (widget.isActive) ..._buildParticles(),

                  // Main icon with shimmer effect
                  Opacity(
                    opacity: _opacityAnimation.value,
                    child: ShaderMask(
                      shaderCallback: (Rect bounds) {
                        return LinearGradient(
                          colors: [
                            widget.color,
                            Colors.purple.shade300,
                            Colors.blue.shade300,
                            widget.color,
                          ],
                          stops: const [0.0, 0.3, 0.7, 1.0],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          tileMode: TileMode.mirror,
                          transform: GradientRotation(_controller.value * 2 * pi),
                        ).createShader(bounds);
                      },
                      child: Icon(
                        Icons.auto_awesome,
                        color: Colors.white,
                        size: widget.size,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  List<Widget> _buildParticles() {
    final List<Widget> particles = [];
    final int particleCount = 5;

    for (int i = 0; i < particleCount; i++) {
      final double angle = _random.nextDouble() * 2 * pi;
      final double distance = (widget.size / 2) * (0.6 + _controller.value * 0.4);
      final double x = cos(angle) * distance;
      final double y = sin(angle) * distance;

      final double particleSize = widget.size * 0.1 * _random.nextDouble() * _controller.value;

      if (particleSize > 0.5) {
        particles.add(
          Positioned(
            left: widget.size / 2 + x,
            top: widget.size / 2 + y,
            child: Opacity(
              opacity: (1.0 - _controller.value).clamp(0.0, 0.7),
              child: Container(
                width: particleSize,
                height: particleSize,
                decoration: BoxDecoration(
                  color: _particleColors[_random.nextInt(_particleColors.length)],
                  shape: BoxShape.circle,
                ),
              ),
            ),
          ),
        );
      }
    }

    return particles;
  }
}
