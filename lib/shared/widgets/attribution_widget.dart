import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:pixs/features/home/<USER>/models/image/image_response.dart';
import 'package:pixs/shared/app/extension/helper.dart';
import 'package:pixs/shared/constants/colors.dart';

class AttributionWidget extends StatelessWidget {
  final ImageResponse image;
  final bool isCompact;
  final Color? textColor;
  final double? fontSize;

  const AttributionWidget({
    super.key,
    required this.image,
    this.isCompact = false,
    this.textColor,
    this.fontSize,
  });

  @override
  Widget build(BuildContext context) {
    final color = textColor ?? kWhite;
    final size = fontSize ?? (isCompact ? 10.sp : 12.sp);

    if (isCompact) {
      return _buildCompactAttribution(color, size);
    } else {
      return _buildFullAttribution(color, size);
    }
  }

  Widget _buildCompactAttribution(Color color, double size) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.6),
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Flexible(
            child: RichText(
              overflow: TextOverflow.ellipsis,
              text: TextSpan(
                style: TextStyle(
                  color: color.withValues(alpha: 0.8),
                  fontSize: size,
                  fontWeight: FontWeight.w300,
                ),
                children: [
                  const TextSpan(text: 'Photo by '),
                  WidgetSpan(
                    child: GestureDetector(
                      onTap: () => _openPhotographerProfile(),
                      child: Text(
                        image.user?.name ?? 'Unknown',
                        style: TextStyle(
                          color: color,
                          fontSize: size,
                          fontWeight: FontWeight.w500,
                          decoration: TextDecoration.underline,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ),
                  const TextSpan(text: ' on '),
                  WidgetSpan(
                    child: GestureDetector(
                      onTap: () => _openUnsplash(),
                      child: Text(
                        'Unsplash',
                        style: TextStyle(
                          color: color,
                          fontSize: size,
                          fontWeight: FontWeight.w500,
                          decoration: TextDecoration.underline,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFullAttribution(Color color, double size) {
    return Container(
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.7),
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            children: [
              if (image.user?.profileImage?.small != null)
                CircleAvatar(
                  radius: 12.r,
                  backgroundImage: NetworkImage(image.user!.profileImage!.small!),
                ),
              SizedBox(width: 8.w),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    RichText(
                      text: TextSpan(
                        style: TextStyle(
                          color: color,
                          fontSize: size,
                          fontWeight: FontWeight.w300,
                        ),
                        children: [
                          const TextSpan(text: 'Photo by '),
                          WidgetSpan(
                            child: GestureDetector(
                              onTap: () => _openPhotographerProfile(),
                              child: Text(
                                image.user?.name ?? 'Unknown',
                                style: TextStyle(
                                  color: color,
                                  fontSize: size,
                                  fontWeight: FontWeight.w600,
                                  decoration: TextDecoration.underline,
                                ),
                              ),
                            ),
                          ),
                          const TextSpan(text: ' on '),
                          WidgetSpan(
                            child: GestureDetector(
                              onTap: () => _openUnsplash(),
                              child: Text(
                                'Unsplash',
                                style: TextStyle(
                                  color: color,
                                  fontSize: size,
                                  fontWeight: FontWeight.w600,
                                  decoration: TextDecoration.underline,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    if (image.user?.username != null)
                      GestureDetector(
                        onTap: () => _openPhotographerProfile(),
                        child: Text(
                          '@${image.user!.username}',
                          style: TextStyle(
                            color: color.withValues(alpha: 0.8),
                            fontSize: size - 1.sp,
                            fontWeight: FontWeight.w400,
                          ),
                        ),
                      ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  void _openPhotographerProfile() {
    if (image.user?.links?.html != null) {
      final url = '${image.user!.links!.html}?utm_source=pixs&utm_medium=referral';
      Helper().launchUrls(url);
    }
  }

  void _openUnsplash() {
    const url = 'https://unsplash.com?utm_source=pixs&utm_medium=referral';
    Helper().launchUrls(url);
  }
}

/// Compact attribution for grid views
class CompactAttributionWidget extends StatelessWidget {
  final ImageResponse image;

  const CompactAttributionWidget({
    super.key,
    required this.image,
  });

  @override
  Widget build(BuildContext context) {
    return AttributionWidget(
      image: image,
      isCompact: true,
      textColor: kWhite,
      fontSize: 9.sp,
    );
  }
}

/// Full attribution for detail views
class DetailAttributionWidget extends StatelessWidget {
  final ImageResponse image;

  const DetailAttributionWidget({
    super.key,
    required this.image,
  });

  @override
  Widget build(BuildContext context) {
    return AttributionWidget(
      image: image,
      isCompact: false,
      textColor: kWhite,
      fontSize: 12.sp,
    );
  }
}
