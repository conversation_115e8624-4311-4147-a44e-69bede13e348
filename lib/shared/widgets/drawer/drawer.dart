import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:pixs/shared/constants/colors.dart';
import 'package:pixs/shared/constants/images.dart';
import 'package:pixs/shared/themes/font_palette.dart';

class DrawerMenu extends StatelessWidget {
  const DrawerMenu({super.key});

  @override
  Widget build(BuildContext context) {
    return Drawer(
      backgroundColor: kWhite,
      child: Column(
        children: <Widget>[
          41.verticalSpace,
          Image.asset(
            Assets.kLogo,
            height: 86.h,
            width: 164.w,
          ),
          71.verticalSpace,
          ListTile(
            title: Text(
              'T&C',
              style: FontPalette.urbenist16.copyWith(
                color: kBlack,
              ),
            ),
            onTap: () {
              Navigator.pop(context);
            },
          ),
          ListTile(
            title: Text(
              'Privacy Policy',
              style: FontPalette.urbenist16.copyWith(
                color: kBlack,
              ),
            ),
            onTap: () {
              Navigator.pop(context);
            },
          ),
          ListTile(
            title: Text(
              'About Us',
              style: FontPalette.urbenist16.copyWith(
                color: kBlack,
              ),
            ),
            onTap: () {
              Navigator.pop(context);
            },
          ),
          ListTile(
            title: Text(
              'Contact Us',
              style: FontPalette.urbenist16.copyWith(
                color: kBlack,
              ),
            ),
            onTap: () {
              Navigator.pop(context);
            },
          ),
          const Spacer(),
          Padding(
            padding: EdgeInsets.only(bottom: 30.h),
            child: Text(
              '@ All rights reserved ',
              style: FontPalette.urbenist12.copyWith(
                color: kSecondaryColor2,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }
}
