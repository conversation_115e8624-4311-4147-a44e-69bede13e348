import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:pixs/shared/constants/colors.dart';
import 'package:pixs/shared/themes/font_palette.dart';

class AnimatedPaginationLoader extends StatefulWidget {
  final bool isLoading;
  final bool hasMore;
  final String? loadingText;
  final String? endText;

  const AnimatedPaginationLoader({
    super.key,
    required this.isLoading,
    required this.hasMore,
    this.loadingText,
    this.endText,
  });

  @override
  State<AnimatedPaginationLoader> createState() => _AnimatedPaginationLoaderState();
}

class _AnimatedPaginationLoaderState extends State<AnimatedPaginationLoader>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late AnimationController _fadeController;
  late AnimationController _scaleController;
  late AnimationController _rotationController;
  
  late Animation<double> _pulseAnimation;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;
  late Animation<double> _rotationAnimation;

  @override
  void initState() {
    super.initState();
    
    // Pulse animation for the loading dots
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );
    
    // Fade animation for text transitions
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    
    // Scale animation for the loader container
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );
    
    // Rotation animation for the circular loader
    _rotationController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );

    _pulseAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));

    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: Curves.elasticOut,
    ));

    _rotationAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _rotationController,
      curve: Curves.linear,
    ));

    _startAnimations();
  }

  void _startAnimations() {
    if (widget.isLoading) {
      _pulseController.repeat(reverse: true);
      _rotationController.repeat();
      _scaleController.forward();
      _fadeController.forward();
    } else {
      _pulseController.stop();
      _rotationController.stop();
      _fadeController.forward();
    }
  }

  @override
  void didUpdateWidget(AnimatedPaginationLoader oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.isLoading != widget.isLoading) {
      _startAnimations();
    }
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _fadeController.dispose();
    _scaleController.dispose();
    _rotationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (!widget.isLoading && !widget.hasMore) {
      return _buildEndMessage();
    }

    if (!widget.isLoading) {
      return const SizedBox.shrink();
    }

    return AnimatedBuilder(
      animation: Listenable.merge([
        _scaleAnimation,
        _fadeAnimation,
      ]),
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Opacity(
            opacity: _fadeAnimation.value,
            child: Container(
              padding: EdgeInsets.symmetric(vertical: 24.h, horizontal: 16.w),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  _buildAnimatedLoader(),
                  16.verticalSpace,
                  _buildLoadingText(),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildAnimatedLoader() {
    return SizedBox(
      height: 40.h,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          _buildCircularLoader(),
          16.horizontalSpace,
          _buildPulsingDots(),
        ],
      ),
    );
  }

  Widget _buildCircularLoader() {
    return AnimatedBuilder(
      animation: _rotationAnimation,
      builder: (context, child) {
        return Transform.rotate(
          angle: _rotationAnimation.value * 2 * 3.14159,
          child: Container(
            width: 32.w,
            height: 32.h,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              gradient: LinearGradient(
                colors: [
                  kPrimaryColor,
                  kPrimaryColor.withOpacity(0.3),
                  Colors.transparent,
                  Colors.transparent,
                ],
                stops: const [0.0, 0.5, 0.5, 1.0],
              ),
            ),
            child: Container(
              margin: EdgeInsets.all(4.w),
              decoration: const BoxDecoration(
                shape: BoxShape.circle,
                color: Color(0xFF1E1D22),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildPulsingDots() {
    return AnimatedBuilder(
      animation: _pulseAnimation,
      builder: (context, child) {
        return Row(
          mainAxisSize: MainAxisSize.min,
          children: List.generate(3, (index) {
            final delay = index * 0.2;
            final animationValue = (_pulseAnimation.value - delay).clamp(0.0, 1.0);
            final opacity = (animationValue * 2).clamp(0.0, 1.0);
            final scale = 0.5 + (animationValue * 0.5);
            
            return Container(
              margin: EdgeInsets.symmetric(horizontal: 2.w),
              child: Transform.scale(
                scale: scale,
                child: Container(
                  width: 8.w,
                  height: 8.h,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: kPrimaryColor.withOpacity(opacity),
                    boxShadow: [
                      BoxShadow(
                        color: kPrimaryColor.withOpacity(opacity * 0.5),
                        blurRadius: 4.0,
                        spreadRadius: 1.0,
                      ),
                    ],
                  ),
                ),
              ),
            );
          }),
        );
      },
    );
  }

  Widget _buildLoadingText() {
    return AnimatedBuilder(
      animation: _fadeAnimation,
      builder: (context, child) {
        return Opacity(
          opacity: _fadeAnimation.value,
          child: Text(
            widget.loadingText ?? 'Loading more collections...',
            style: FontPalette.urbenist14.copyWith(
              color: kWhite.withOpacity(0.7),
            ),
            textAlign: TextAlign.center,
          ),
        );
      },
    );
  }

  Widget _buildEndMessage() {
    return AnimatedBuilder(
      animation: _fadeAnimation,
      builder: (context, child) {
        return Opacity(
          opacity: _fadeAnimation.value,
          child: Container(
            padding: EdgeInsets.symmetric(vertical: 32.h, horizontal: 16.w),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  width: 48.w,
                  height: 48.h,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: kPrimaryColor.withOpacity(0.1),
                    border: Border.all(
                      color: kPrimaryColor.withOpacity(0.3),
                      width: 2.w,
                    ),
                  ),
                  child: Icon(
                    Icons.check_circle_outline,
                    color: kPrimaryColor,
                    size: 24.sp,
                  ),
                ),
                16.verticalSpace,
                Text(
                  widget.endText ?? 'You\'ve reached the end!',
                  style: FontPalette.urbenist16.copyWith(
                    color: kWhite.withOpacity(0.8),
                    fontWeight: FontWeight.w600,
                  ),
                  textAlign: TextAlign.center,
                ),
                8.verticalSpace,
                Text(
                  'No more collections to load',
                  style: FontPalette.urbenist12.copyWith(
                    color: kWhite.withOpacity(0.5),
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
