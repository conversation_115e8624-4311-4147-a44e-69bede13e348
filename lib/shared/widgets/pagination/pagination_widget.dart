import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:pixs/shared/widgets/pagination/animated_pagination_loader.dart';

class PaginationWidget extends StatelessWidget {
  const PaginationWidget({
    super.key,
    required this.onPagination,
    required this.isPaginating,
    required this.child,
    required this.next,
    this.loadingText,
    this.endText,
    this.useAnimatedLoader = true,
  });

  final bool Function(ScrollEndNotification) onPagination;
  final bool isPaginating;
  final Widget child;
  final bool next;
  final String? loadingText;
  final String? endText;
  final bool useAnimatedLoader;

  @override
  Widget build(BuildContext context) {
    return NotificationListener<ScrollEndNotification>(
      onNotification: (notification) {
        if (next &&
            notification.metrics.pixels ==
                notification.metrics.maxScrollExtent) {
          return onPagination.call(notification);
        } else {
          return false;
        }
      },
      child: Column(
        children: [
          Expanded(child: child),
          if (useAnimatedLoader)
            AnimatedPaginationLoader(
              isLoading: isPaginating,
              hasMore: next,
              loadingText: loadingText,
              endText: endText,
            )
          else if (isPaginating)
            const Padding(
              padding: EdgeInsets.all(8),
              child: CupertinoActivityIndicator(),
            )
        ],
      ),
    );
  }
}
