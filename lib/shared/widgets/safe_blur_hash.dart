import 'package:flutter/material.dart';
import 'package:flutter_blurhash/flutter_blurhash.dart' as blurhash;

/// A safe wrapper around the BlurHash widget that validates the hash string
/// before creating the actual BlurHash widget. This prevents the flutter_blurhash
/// validation error when invalid hash strings are provided.
class SafeBlurHash extends StatelessWidget {
  final String? hash;
  final Curve curve;
  final Duration duration;
  final VoidCallback? onReady;
  final Widget? fallback;

  const SafeBlurHash({
    super.key,
    required this.hash,
    this.curve = Curves.easeOut,
    this.duration = const Duration(milliseconds: 1000),
    this.onReady,
    this.fallback,
  });

  @override
  Widget build(BuildContext context) {
    // Validate hash before creating the actual BlurHash widget
    if (hash == null || hash!.isEmpty || hash!.length < 6) {
      return fallback ?? const CircularProgressIndicator();
    }
    
    return blurhash.BlurHash(
      hash: hash!,
      curve: curve,
      duration: duration,
      onReady: onReady,
    );
  }
}
