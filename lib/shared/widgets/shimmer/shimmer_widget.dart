import 'package:flutter/material.dart';
import 'package:pixs/shared/constants/colors.dart';
import 'package:shimmer/shimmer.dart';

class ShimmerWidget extends StatelessWidget {
  const ShimmerWidget({
    super.key,
    this.child,
    this.isLoading = true,
    this.isError = false,
    this.shimmerChild,
    this.width,
    this.height,
    this.color = kWhite,
    this.baseColor,
    this.highlightColor,
    this.radius = 4,
  });

  final Widget? child;
  final Widget? shimmerChild;
  final bool isLoading, isError;
  final double? width, height;
  final double radius;
  final Color? color, baseColor, highlightColor;

  @override
  Widget build(BuildContext context) {
    if (isError) {
      return const SizedBox();
    }
    if (isLoading) {
      return ClipRRect(
        borderRadius: BorderRadius.circular(radius),
        child: Shimmer.fromColors(
          baseColor: baseColor ?? const Color(0xFF1E1D22),
          highlightColor: highlightColor ?? const Color(0xFF292834),
          child: height != null
              ? Container(
                  decoration: BoxDecoration(
                    color: color,
                    borderRadius: BorderRadius.circular(radius),
                    boxShadow: const [
                      BoxShadow(
                        color: Colors.black,
                        blurRadius: 10,
                      ),
                    ],
                  ),
                  width: width ?? double.infinity,
                  height: height ?? double.infinity,
                )
              : shimmerChild ?? child ?? const SizedBox(),
        ),
      );
    } else if (!isLoading) {
      return child ?? const SizedBox();
    } else {
      return const SizedBox();
    }
  }
}
