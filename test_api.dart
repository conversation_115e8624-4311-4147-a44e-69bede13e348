import 'dart:convert';
import 'dart:io';

void main() async {
  // Test the Unsplash API directly
  final client = HttpClient();
  
  try {
    // First, let's get collections
    print('Testing collections API...');
    final collectionsRequest = await client.getUrl(
      Uri.parse('https://api.unsplash.com/collections?page=1&per_page=5')
    );
    collectionsRequest.headers.set('Authorization', 'Client-ID YOUR_ACCESS_KEY');
    
    final collectionsResponse = await collectionsRequest.close();
    final collectionsBody = await collectionsResponse.transform(utf8.decoder).join();
    
    if (collectionsResponse.statusCode == 200) {
      final collections = jsonDecode(collectionsBody) as List;
      print('Collections API working! Found ${collections.length} collections');
      
      if (collections.isNotEmpty) {
        final firstCollection = collections.first;
        final collectionId = firstCollection['id'];
        final totalPhotos = firstCollection['total_photos'];
        print('First collection: ${firstCollection['title']} (ID: $collectionId, Photos: $totalPhotos)');
        
        // Now test collection photos API
        print('\nTesting collection photos API...');
        final photosRequest = await client.getUrl(
          Uri.parse('https://api.unsplash.com/collections/$collectionId/photos?page=1&per_page=30')
        );
        photosRequest.headers.set('Authorization', 'Client-ID YOUR_ACCESS_KEY');
        
        final photosResponse = await photosRequest.close();
        final photosBody = await photosResponse.transform(utf8.decoder).join();
        
        if (photosResponse.statusCode == 200) {
          final photos = jsonDecode(photosBody) as List;
          print('Collection photos API working! Found ${photos.length} photos');
          
          if (photos.isEmpty) {
            print('WARNING: Collection has no photos despite showing $totalPhotos total photos');
          }
        } else {
          print('Collection photos API failed: ${photosResponse.statusCode}');
          print('Response: $photosBody');
        }
      }
    } else {
      print('Collections API failed: ${collectionsResponse.statusCode}');
      print('Response: $collectionsBody');
    }
  } catch (e) {
    print('Error: $e');
  } finally {
    client.close();
  }
}
